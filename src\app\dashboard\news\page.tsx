"use client";

import { api } from "@convex/api";
import { useQuery } from "convex/react";
import {
  Clock,
  Edit,
  Eye,
  FileText,
  Globe,
  Grid3X3,
  Image as ImageIcon,
  List,
  MoreHorizontal,
  Plus,
  Search,
  SlidersHorizontal,
  Tag,
  Trash2,
  TrendingUp,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";

type ViewMode = "grid" | "list";

export default function NewsPage() {
  const t = useTranslations("news");
  const locale = useLocale();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedLanguage, setSelectedLanguage] = useState<string>(locale);
  const [viewMode, setViewMode] = useState<ViewMode>("grid");

  // Fetch news articles and categories
  const articles = useQuery(api.news.getNewsArticles, {
    categoryId:
      selectedCategory === "all" ? undefined : (selectedCategory as any),
    status: selectedStatus === "all" ? undefined : (selectedStatus as any),
    language: selectedLanguage,
    limit: 20,
  });

  const categories = useQuery(api.news.getNewsCategories, {
    includeInactive: false,
    language: selectedLanguage,
  });

  const filteredArticles =
    articles?.filter(
      (article) =>
        article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        article.summary?.toLowerCase().includes(searchTerm.toLowerCase()),
    ) || [];

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "published":
        return "bg-green-50 text-green-700 border-green-200 hover:bg-green-100";
      case "draft":
        return "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100";
      case "archived":
        return "bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100";
      default:
        return "bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100";
    }
  };

  const formatDate = (timestamp?: number) => {
    if (!timestamp) return t("status.unpublished");
    return new Date(timestamp).toLocaleDateString(
      selectedLanguage === "zh" ? "zh-CN" : "en-US",
      {
        year: "numeric",
        month: "short",
        day: "numeric",
      },
    );
  };

  const formatRelativeTime = (timestamp?: number) => {
    if (!timestamp) return "";
    const now = new Date();
    const date = new Date(timestamp);
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return t("time.hours_ago", { hours: Math.floor(diffInHours) });
    } else if (diffInHours < 24 * 7) {
      return t("time.days_ago", { days: Math.floor(diffInHours / 24) });
    } else {
      return formatDate(timestamp);
    }
  };

  // Cover Image Component
  const CoverImage = ({
    coverImageId,
    title,
    className = "",
  }: {
    coverImageId?: string;
    title: string;
    className?: string;
  }) => {
    const coverImageInfo = useQuery(
      api.media.getMediaFileByStorageId,
      coverImageId ? { storageId: coverImageId as any } : "skip",
    );

    if (!coverImageId || !coverImageInfo?.url) {
      return (
        <div
          className={`flex items-center justify-center bg-gray-100 ${className}`}
        >
          <ImageIcon className="h-8 w-8 text-gray-400" />
        </div>
      );
    }

    return (
      <div className={`relative overflow-hidden ${className}`}>
        <Image
          src={coverImageInfo.url}
          alt={title}
          fill
          className="object-cover transition-transform duration-200 group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>
    );
  };

  // Statistics
  const totalArticles = filteredArticles.length;
  const publishedCount = filteredArticles.filter(
    (a) => a.status === "published",
  ).length;
  const draftCount = filteredArticles.filter(
    (a) => a.status === "draft",
  ).length;
  const totalViews = filteredArticles.reduce(
    (sum, article) => sum + (article.viewCount || 0),
    0,
  );

  return (
    <div className="min-h-screen bg-gray-50/50">
      <div className="mx-auto max-w-7xl space-y-8 p-6">
        {/* Header */}
        <div className="flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between">
          <div className="space-y-1">
            <h1 className="font-bold text-3xl text-gray-900 tracking-tight">
              {t("title")}
            </h1>
            <p className="text-gray-600">{t("description")}</p>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1 rounded-lg border bg-white p-1">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="h-8 px-3"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="h-8 px-3"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
            <Link href="/dashboard/news/create">
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="mr-2 h-4 w-4" />
                {t("actions.create")}
              </Button>
            </Link>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <Card className="border-0 bg-white shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="rounded-full bg-blue-50 p-3">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="font-medium text-gray-600 text-sm">
                    {t("stats.total_articles")}
                  </p>
                  <p className="font-bold text-2xl text-gray-900">
                    {totalArticles}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-white shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="rounded-full bg-green-50 p-3">
                  <Globe className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="font-medium text-gray-600 text-sm">
                    {t("stats.published")}
                  </p>
                  <p className="font-bold text-2xl text-gray-900">
                    {publishedCount}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-white shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="rounded-full bg-amber-50 p-3">
                  <Edit className="h-6 w-6 text-amber-600" />
                </div>
                <div className="ml-4">
                  <p className="font-medium text-gray-600 text-sm">
                    {t("stats.drafts")}
                  </p>
                  <p className="font-bold text-2xl text-gray-900">
                    {draftCount}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-white shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="rounded-full bg-purple-50 p-3">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="font-medium text-gray-600 text-sm">
                    {t("stats.total_views")}
                  </p>
                  <p className="font-bold text-2xl text-gray-900">
                    {totalViews.toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="border-0 bg-white shadow-sm">
          <CardContent className="p-6">
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-2">
                <SlidersHorizontal className="h-4 w-4 text-gray-500" />
                <span className="font-medium text-gray-700 text-sm">
                  {t("filters.title")}
                </span>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
                {/* Search */}
                <div className="relative lg:col-span-2">
                  <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder={t("filters.search_placeholder")}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="border-gray-200 pl-10 focus:border-blue-400 focus:ring-blue-400"
                  />
                </div>

                {/* Category Filter */}
                <Select
                  value={selectedCategory}
                  onValueChange={setSelectedCategory}
                >
                  <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
                    <SelectValue placeholder={t("filters.category")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      {t("filters.all_categories")}
                    </SelectItem>
                    {categories?.map((category) => (
                      <SelectItem key={category._id} value={category._id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Status Filter */}
                <Select
                  value={selectedStatus}
                  onValueChange={setSelectedStatus}
                >
                  <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
                    <SelectValue placeholder={t("filters.status")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      {t("filters.all_statuses")}
                    </SelectItem>
                    <SelectItem value="published">
                      {t("status.published")}
                    </SelectItem>
                    <SelectItem value="draft">{t("status.draft")}</SelectItem>
                    <SelectItem value="archived">
                      {t("status.archived")}
                    </SelectItem>
                  </SelectContent>
                </Select>

                {/* Language Filter */}
                <Select
                  value={selectedLanguage}
                  onValueChange={setSelectedLanguage}
                >
                  <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
                    <SelectValue placeholder={t("filters.language")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">{t("languages.en")}</SelectItem>
                    <SelectItem value="zh">{t("languages.zh")}</SelectItem>
                    <SelectItem value="ja">{t("languages.ja")}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Articles Section */}
        {filteredArticles.length === 0 ? (
          <Card className="border-0 bg-white shadow-sm">
            <CardContent className="flex flex-col items-center justify-center py-16">
              <div className="mb-6 rounded-full bg-gray-100 p-6">
                <FileText className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="mb-2 font-semibold text-gray-900 text-xl">
                {t("empty.title")}
              </h3>
              <p className="mb-6 max-w-md text-center text-gray-600">
                {t("empty.description")}
              </p>
              <Link href="/dashboard/news/create">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="mr-2 h-4 w-4" />
                  {t("actions.create_first")}
                </Button>
              </Link>
            </CardContent>
          </Card>
        ) : viewMode === "grid" ? (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredArticles.map((article) => (
              <Card
                key={article._id}
                className="group cursor-pointer border-0 bg-white shadow-sm transition-all duration-200 hover:scale-[1.02] hover:shadow-md"
              >
                <CardContent className="p-0">
                  {/* Cover Image */}
                  <CoverImage
                    coverImageId={article.coverImageId}
                    title={article.title}
                    className="h-48 w-full rounded-t-lg"
                  />

                  {/* Header */}
                  <div className="p-6 pb-4">
                    <div className="mb-3 flex items-start justify-between">
                      <Badge
                        className={`${getStatusColor(article.status)} transition-colors`}
                      >
                        {t(`status.${article.status || "draft"}`)}
                      </Badge>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 opacity-0 transition-opacity group-hover:opacity-100"
                          >
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuLabel>
                            {t("actions.title")}
                          </DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild>
                            <Link href={`/dashboard/news/${article._id}`}>
                              <Eye className="mr-2 h-4 w-4" />
                              {t("actions.view")}
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/dashboard/news/${article._id}/edit`}>
                              <Edit className="mr-2 h-4 w-4" />
                              {t("actions.edit")}
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600 focus:text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" />
                            {t("actions.delete")}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    <h3 className="mb-2 line-clamp-2 font-semibold text-gray-900 text-lg transition-colors group-hover:text-blue-600">
                      {article.title}
                    </h3>

                    {article.summary && (
                      <p className="mb-4 line-clamp-3 text-gray-600 text-sm">
                        {article.summary}
                      </p>
                    )}
                  </div>

                  <Separator />

                  {/* Footer */}
                  <div className="p-6 pt-4">
                    <div className="flex items-center justify-between text-gray-500 text-xs">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <Tag className="h-3 w-3" />
                          <span className="truncate">
                            {article.category.name}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          <span>{article.viewCount || 0}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>
                          {formatRelativeTime(
                            article.publishedAt || article._creationTime,
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="border-0 bg-white shadow-sm">
            <CardContent className="p-0">
              {filteredArticles.map((article, index) => (
                <div key={article._id}>
                  <div className="group p-6 transition-colors hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex min-w-0 flex-1 items-start gap-4">
                        {/* Cover Image Thumbnail */}
                        <CoverImage
                          coverImageId={article.coverImageId}
                          title={article.title}
                          className="h-16 w-16 shrink-0 rounded-lg"
                        />

                        <div className="min-w-0 flex-1">
                          <div className="mb-2 flex items-center gap-3">
                            <h3 className="truncate font-semibold text-gray-900 text-lg transition-colors group-hover:text-blue-600">
                              {article.title}
                            </h3>
                            <Badge
                              className={`${getStatusColor(article.status)} transition-colors`}
                            >
                              {t(`status.${article.status || "draft"}`)}
                            </Badge>
                          </div>

                          <div className="mb-3 flex items-center gap-6 text-gray-500 text-sm">
                            <div className="flex items-center gap-1">
                              <Tag className="h-4 w-4" />
                              <span>{article.category.name}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Eye className="h-4 w-4" />
                              <span>
                                {article.viewCount || 0} {t("time.views")}
                              </span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              <span>
                                {formatRelativeTime(
                                  article.publishedAt || article._creationTime,
                                )}
                              </span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Globe className="h-4 w-4" />
                              <span>{selectedLanguage.toUpperCase()}</span>
                            </div>
                          </div>

                          {article.summary && (
                            <p className="line-clamp-2 text-gray-600 text-sm">
                              {article.summary}
                            </p>
                          )}
                        </div>
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="ml-4">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuLabel>
                            {t("actions.title")}
                          </DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild>
                            <Link href={`/dashboard/news/${article._id}`}>
                              <Eye className="mr-2 h-4 w-4" />
                              {t("actions.view")}
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/dashboard/news/${article._id}/edit`}>
                              <Edit className="mr-2 h-4 w-4" />
                              {t("actions.edit")}
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600 focus:text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" />
                            {t("actions.delete")}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                  {index < filteredArticles.length - 1 && <Separator />}
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
