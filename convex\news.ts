import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// 定义多语言文本类型
const multiLanguageText = v.record(v.string(), v.string());
const multiLanguageArray = v.record(v.string(), v.array(v.string()));

// ============ 新闻分类管理 ============

/**
 * 创建新闻分类
 */
export const createNewsCategory = mutation({
  args: {
    name: multiLanguageText,
    description: multiLanguageText,
    sortOrder: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
    seoKeywords: v.optional(multiLanguageArray),
    seoDescription: v.optional(multiLanguageText),
    slug: v.optional(multiLanguageText),
  },
  returns: v.id("news_categories"),
  handler: async (ctx, args) => {
    return await ctx.db.insert("news_categories", {
      ...args,
      isActive: args.isActive ?? true,
    });
  },
});

/**
 * 更新新闻分类
 */
export const updateNewsCategory = mutation({
  args: {
    categoryId: v.id("news_categories"),
    name: v.optional(multiLanguageText),
    description: v.optional(multiLanguageText),
    sortOrder: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
    seoKeywords: v.optional(multiLanguageArray),
    seoDescription: v.optional(multiLanguageText),
    slug: v.optional(multiLanguageText),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { categoryId, ...updates } = args;
    const category = await ctx.db.get(categoryId);
    if (!category) {
      throw new Error("News category not found");
    }

    await ctx.db.patch(categoryId, updates);
    return null;
  },
});

/**
 * 删除新闻分类
 */
export const deleteNewsCategory = mutation({
  args: {
    categoryId: v.id("news_categories"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const category = await ctx.db.get(args.categoryId);
    if (!category) {
      throw new Error("News category not found");
    }

    // 检查是否有文章使用此分类
    const articlesInCategory = await ctx.db
      .query("news_articles")
      .withIndex("by_category", (q) => q.eq("categoryId", args.categoryId))
      .first();

    if (articlesInCategory) {
      throw new Error("Cannot delete category with existing articles");
    }

    await ctx.db.delete(args.categoryId);
    return null;
  },
});

/**
 * 获取所有新闻分类
 */
export const getNewsCategories = query({
  args: {
    includeInactive: v.optional(v.boolean()),
    language: v.optional(v.string()),
  },
  returns: v.array(
    v.object({
      _id: v.id("news_categories"),
      _creationTime: v.number(),
      name: v.string(),
      description: v.string(),
      sortOrder: v.optional(v.number()),
      isActive: v.optional(v.boolean()),
      seoKeywords: v.optional(v.array(v.string())),
      seoDescription: v.optional(v.string()),
      slug: v.optional(v.string()),
      articleCount: v.number(),
    }),
  ),
  handler: async (ctx, args) => {
    const language = args.language || "en";

    let categories = await ctx.db
      .query("news_categories")
      .withIndex("by_sort_order")
      .collect();

    // 如果不包含非活跃分类，则过滤掉
    if (!args.includeInactive) {
      categories = categories.filter((category) => category.isActive !== false);
    }

    // 统计每个分类下的文章数量
    const categoriesWithCount = await Promise.all(
      categories.map(async (category) => {
        const articles = await ctx.db
          .query("news_articles")
          .withIndex("by_category", (q) => q.eq("categoryId", category._id))
          .collect();

        return {
          _id: category._id,
          _creationTime: category._creationTime,
          name:
            category.name[language] ||
            category.name[Object.keys(category.name)[0]] ||
            "",
          description:
            category.description[language] ||
            category.description[Object.keys(category.description)[0]] ||
            "",
          sortOrder: category.sortOrder,
          isActive: category.isActive,
          seoKeywords: category.seoKeywords?.[language] || [],
          seoDescription: category.seoDescription?.[language] || "",
          slug: category.slug?.[language] || "",
          articleCount: articles.length,
        };
      }),
    );

    // 按排序顺序排列
    return categoriesWithCount.sort((a, b) => {
      const orderA = a.sortOrder ?? a._creationTime;
      const orderB = b.sortOrder ?? b._creationTime;
      return orderA - orderB;
    });
  },
});

/**
 * 获取单个新闻分类的完整多语言数据
 */
export const getNewsCategoryWithAllLanguages = query({
  args: {
    categoryId: v.id("news_categories"),
  },
  returns: v.object({
    _id: v.id("news_categories"),
    _creationTime: v.number(),
    name: v.record(v.string(), v.string()),
    description: v.record(v.string(), v.string()),
    sortOrder: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
    seoKeywords: v.optional(v.record(v.string(), v.array(v.string()))),
    seoDescription: v.optional(v.record(v.string(), v.string())),
    slug: v.optional(v.record(v.string(), v.string())),
  }),
  handler: async (ctx, args) => {
    const category = await ctx.db.get(args.categoryId);
    if (!category) {
      throw new Error("News category not found");
    }

    return {
      _id: category._id,
      _creationTime: category._creationTime,
      name: category.name,
      description: category.description,
      sortOrder: category.sortOrder,
      isActive: category.isActive,
      seoKeywords: category.seoKeywords,
      seoDescription: category.seoDescription,
      slug: category.slug,
    };
  },
});

// ============ 新闻文章管理 ============

/**
 * 创建新闻文章
 */
export const createNewsArticle = mutation({
  args: {
    title: multiLanguageText,
    content: multiLanguageText,
    summary: v.optional(multiLanguageText),
    categoryId: v.id("news_categories"),
    coverImageId: v.optional(v.id("_storage")),
    imageIds: v.optional(v.array(v.id("_storage"))),
    sortOrder: v.optional(v.number()),
    seoKeywords: v.optional(multiLanguageArray),
    seoDescription: v.optional(multiLanguageText),
    slug: v.optional(multiLanguageText),
    originalLanguage: v.string(),
    availableLanguages: v.array(v.string()),
  },
  returns: v.id("news_articles"),
  handler: async (ctx, args) => {
    // 检查分类是否存在
    const category = await ctx.db.get(args.categoryId);
    if (!category) {
      throw new Error("News category not found");
    }

    return await ctx.db.insert("news_articles", {
      ...args,
      status: "draft",
      viewCount: 0,
    });
  },
});

/**
 * 更新新闻文章
 */
export const updateNewsArticle = mutation({
  args: {
    articleId: v.id("news_articles"),
    title: v.optional(multiLanguageText),
    content: v.optional(multiLanguageText),
    summary: v.optional(multiLanguageText),
    categoryId: v.optional(v.id("news_categories")),
    coverImageId: v.optional(v.id("_storage")),
    imageIds: v.optional(v.array(v.id("_storage"))),
    sortOrder: v.optional(v.number()),
    seoKeywords: v.optional(multiLanguageArray),
    seoDescription: v.optional(multiLanguageText),
    slug: v.optional(multiLanguageText),
    availableLanguages: v.optional(v.array(v.string())),
    status: v.optional(
      v.union(
        v.literal("draft"),
        v.literal("published"),
        v.literal("archived"),
      ),
    ),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { articleId, ...updates } = args;
    const article = await ctx.db.get(articleId);
    if (!article) {
      throw new Error("News article not found");
    }

    // 如果更新分类，检查分类是否存在
    if (updates.categoryId) {
      const category = await ctx.db.get(updates.categoryId);
      if (!category) {
        throw new Error("News category not found");
      }
    }

    await ctx.db.patch(articleId, updates);
    return null;
  },
});

/**
 * 发布新闻文章
 */
export const publishNewsArticle = mutation({
  args: {
    articleId: v.id("news_articles"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const article = await ctx.db.get(args.articleId);
    if (!article) {
      throw new Error("News article not found");
    }

    await ctx.db.patch(args.articleId, {
      status: "published",
      publishedAt: Date.now(),
    });
    return null;
  },
});

/**
 * 删除新闻文章
 */
export const deleteNewsArticle = mutation({
  args: {
    articleId: v.id("news_articles"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const article = await ctx.db.get(args.articleId);
    if (!article) {
      throw new Error("News article not found");
    }

    await ctx.db.delete(args.articleId);
    return null;
  },
});

/**
 * 获取新闻文章的完整多语言数据（用于编辑）
 */
export const getNewsArticleWithAllLanguages = query({
  args: {
    articleId: v.id("news_articles"),
  },
  returns: v.union(
    v.object({
      _id: v.id("news_articles"),
      _creationTime: v.number(),
      title: multiLanguageText,
      content: multiLanguageText,
      summary: v.optional(multiLanguageText),
      coverImageId: v.optional(v.id("_storage")),
      imageIds: v.optional(v.array(v.id("_storage"))),
      categoryId: v.id("news_categories"),
      status: v.optional(
        v.union(
          v.literal("draft"),
          v.literal("published"),
          v.literal("archived"),
        ),
      ),
      publishedAt: v.optional(v.number()),
      sortOrder: v.optional(v.number()),
      viewCount: v.optional(v.number()),
      seoKeywords: v.optional(multiLanguageArray),
      seoDescription: v.optional(multiLanguageText),
      slug: v.optional(multiLanguageText),
      originalLanguage: v.string(),
      availableLanguages: v.array(v.string()),
      category: v.object({
        _id: v.id("news_categories"),
        name: multiLanguageText,
        description: multiLanguageText,
      }),
    }),
    v.null(),
  ),
  handler: async (ctx, args) => {
    const article = await ctx.db.get(args.articleId);
    if (!article) {
      return null;
    }

    const category = await ctx.db.get(article.categoryId);
    if (!category) {
      throw new Error("Category not found");
    }

    return {
      _id: article._id,
      _creationTime: article._creationTime,
      title: article.title,
      content: article.content,
      summary: article.summary,
      coverImageId: article.coverImageId,
      imageIds: article.imageIds,
      categoryId: article.categoryId,
      status: article.status,
      publishedAt: article.publishedAt,
      sortOrder: article.sortOrder,
      viewCount: article.viewCount,
      seoKeywords: article.seoKeywords,
      seoDescription: article.seoDescription,
      slug: article.slug,
      originalLanguage: article.originalLanguage,
      availableLanguages: article.availableLanguages,
      category: {
        _id: category._id,
        name: category.name,
        description: category.description,
      },
    };
  },
});

/**
 * 获取新闻文章详情
 */
export const getNewsArticle = query({
  args: {
    articleId: v.id("news_articles"),
    language: v.optional(v.string()),
  },
  returns: v.union(
    v.object({
      _id: v.id("news_articles"),
      _creationTime: v.number(),
      title: v.string(),
      content: v.string(),
      summary: v.optional(v.string()),
      coverImageId: v.optional(v.id("_storage")),
      imageIds: v.optional(v.array(v.id("_storage"))),
      categoryId: v.id("news_categories"),
      status: v.optional(
        v.union(
          v.literal("draft"),
          v.literal("published"),
          v.literal("archived"),
        ),
      ),
      publishedAt: v.optional(v.number()),
      sortOrder: v.optional(v.number()),
      viewCount: v.optional(v.number()),
      seoKeywords: v.optional(v.array(v.string())),
      seoDescription: v.optional(v.string()),
      slug: v.optional(v.string()),
      originalLanguage: v.string(),
      availableLanguages: v.array(v.string()),
      category: v.object({
        _id: v.id("news_categories"),
        name: v.string(),
        description: v.string(),
      }),
    }),
    v.null(),
  ),
  handler: async (ctx, args) => {
    const language = args.language || "en";
    const article = await ctx.db.get(args.articleId);
    if (!article) {
      return null;
    }

    const category = await ctx.db.get(article.categoryId);
    if (!category) {
      throw new Error("Category not found");
    }

    return {
      _id: article._id,
      _creationTime: article._creationTime,
      title:
        article.title[language] ||
        article.title[article.originalLanguage] ||
        "",
      content:
        article.content[language] ||
        article.content[article.originalLanguage] ||
        "",
      summary:
        article.summary?.[language] ||
        article.summary?.[article.originalLanguage],
      coverImageId: article.coverImageId,
      imageIds: article.imageIds,
      categoryId: article.categoryId,
      status: article.status,
      publishedAt: article.publishedAt,
      sortOrder: article.sortOrder,
      viewCount: article.viewCount,
      seoKeywords: article.seoKeywords?.[language] || [],
      seoDescription: article.seoDescription?.[language],
      slug: article.slug?.[language],
      originalLanguage: article.originalLanguage,
      availableLanguages: article.availableLanguages,
      category: {
        _id: category._id,
        name:
          category.name[language] ||
          category.name[Object.keys(category.name)[0]] ||
          "",
        description:
          category.description[language] ||
          category.description[Object.keys(category.description)[0]] ||
          "",
      },
    };
  },
});

/**
 * 获取新闻文章列表
 */
export const getNewsArticles = query({
  args: {
    categoryId: v.optional(v.id("news_categories")),
    status: v.optional(
      v.union(
        v.literal("draft"),
        v.literal("published"),
        v.literal("archived"),
      ),
    ),
    language: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("news_articles"),
      _creationTime: v.number(),
      title: v.string(),
      summary: v.optional(v.string()),
      coverImageId: v.optional(v.id("_storage")),
      publishedAt: v.optional(v.number()),
      viewCount: v.optional(v.number()),
      slug: v.optional(v.string()),
      status: v.optional(
        v.union(
          v.literal("draft"),
          v.literal("published"),
          v.literal("archived"),
        ),
      ),
      category: v.object({
        _id: v.id("news_categories"),
        name: v.string(),
        description: v.string(),
      }),
    }),
  ),
  handler: async (ctx, args) => {
    const language = args.language || "en";

    // 获取所有文章，根据条件筛选
    let articles = await ctx.db.query("news_articles").order("desc").take(1000);

    // 根据条件筛选
    if (args.categoryId) {
      articles = articles.filter((a) => a.categoryId === args.categoryId);
    }
    if (args.status) {
      articles = articles.filter((a) => a.status === args.status);
    }

    // 应用限制
    articles = articles.slice(0, args.limit || 20);

    // 获取分类信息
    const articlesWithCategory = await Promise.all(
      articles.map(async (article) => {
        const category = await ctx.db.get(article.categoryId);
        if (!category) {
          throw new Error("Category not found");
        }
        return {
          _id: article._id,
          _creationTime: article._creationTime,
          title:
            article.title[language] ||
            article.title[article.originalLanguage] ||
            "",
          summary:
            article.summary?.[language] ||
            article.summary?.[article.originalLanguage],
          coverImageId: article.coverImageId,
          publishedAt: article.publishedAt,
          viewCount: article.viewCount,
          slug: article.slug?.[language],
          status: article.status,
          category: {
            _id: category._id,
            name:
              category.name[language] ||
              category.name[Object.keys(category.name)[0]] ||
              "",
            description:
              category.description[language] ||
              category.description[Object.keys(category.description)[0]] ||
              "",
          },
        };
      }),
    );

    return articlesWithCategory;
  },
});

/**
 * 搜索新闻文章
 */
export const searchNewsArticles = query({
  args: {
    searchTerm: v.string(),
    categoryId: v.optional(v.id("news_categories")),
    language: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("news_articles"),
      title: v.string(),
      summary: v.optional(v.string()),
      coverImageId: v.optional(v.id("_storage")),
      publishedAt: v.optional(v.number()),
      slug: v.optional(v.string()),
      status: v.optional(
        v.union(
          v.literal("draft"),
          v.literal("published"),
          v.literal("archived"),
        ),
      ),
      category: v.object({
        _id: v.id("news_categories"),
        name: v.string(),
        description: v.string(),
      }),
    }),
  ),
  handler: async (ctx, args) => {
    const language = args.language || "en";

    // 获取所有已发布文章，根据条件筛选
    let articles = await ctx.db
      .query("news_articles")
      .filter((q) => q.eq(q.field("status"), "published"))
      .take(args.limit || 50);

    // 如果指定了分类，进一步筛选
    if (args.categoryId) {
      articles = articles.filter((a) => a.categoryId === args.categoryId);
    }

    // 多语言文本搜索
    const searchTerm = args.searchTerm.toLowerCase();
    const filteredArticles = articles.filter((article) => {
      const title =
        article.title[language] ||
        article.title[article.originalLanguage] ||
        "";
      const content =
        article.content[language] ||
        article.content[article.originalLanguage] ||
        "";
      const summary =
        article.summary?.[language] ||
        article.summary?.[article.originalLanguage] ||
        "";

      return (
        title.toLowerCase().includes(searchTerm) ||
        content.toLowerCase().includes(searchTerm) ||
        summary.toLowerCase().includes(searchTerm)
      );
    });

    // 获取分类信息
    const articlesWithCategory = await Promise.all(
      filteredArticles.map(async (article) => {
        const category = await ctx.db.get(article.categoryId);
        if (!category) {
          throw new Error("Category not found");
        }
        return {
          _id: article._id,
          title:
            article.title[language] ||
            article.title[article.originalLanguage] ||
            "",
          summary:
            article.summary?.[language] ||
            article.summary?.[article.originalLanguage],
          coverImageId: article.coverImageId,
          publishedAt: article.publishedAt,
          slug: article.slug?.[language],
          status: article.status,
          category: {
            _id: category._id,
            name:
              category.name[language] ||
              category.name[Object.keys(category.name)[0]] ||
              "",
            description:
              category.description[language] ||
              category.description[Object.keys(category.description)[0]] ||
              "",
          },
        };
      }),
    );

    return articlesWithCategory;
  },
});

/**
 * 增加文章浏览次数
 */
export const incrementArticleViews = mutation({
  args: {
    articleId: v.id("news_articles"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const article = await ctx.db.get(args.articleId);
    if (!article) {
      throw new Error("Article not found");
    }

    await ctx.db.patch(args.articleId, {
      viewCount: (article.viewCount || 0) + 1,
    });
    return null;
  },
});
