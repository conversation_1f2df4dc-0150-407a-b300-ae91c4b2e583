"use client";

import { Check, Globe, Languages } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useTransition } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { localeLabels, routing } from "@/i18n/routing";
import { setLocaleCookie } from "@/i18n/locale-utils";

type LanguageSwitcherProps = {
  variant?: "dropdown" | "select" | "compact";
  className?: string;
};

export function LanguageSwitcher({
  variant = "dropdown",
  className = "",
}: LanguageSwitcherProps) {
  const currentLocale = useLocale();
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const _t = useTranslations("common");

  const handleLocaleChange = (locale: string) => {
    if (locale === currentLocale) return;

    // Set cookie to persist the locale choice
    setLocaleCookie(locale);

    // Trigger route refresh with new locale
    startTransition(() => {
      router.refresh();
    });
  };

  if (variant === "select") {
    return (
      <Select
        value={currentLocale}
        onValueChange={handleLocaleChange}
        disabled={isPending}
      >
        <SelectTrigger className={className}>
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            <SelectValue />
          </div>
        </SelectTrigger>
        <SelectContent>
          {routing.locales.map((locale) => (
            <SelectItem key={locale} value={locale}>
              <div className="flex items-center gap-2">
                <span className="font-medium text-sm">
                  {localeLabels[locale as keyof typeof localeLabels]}
                </span>
                {locale === currentLocale && (
                  <Check className="h-3 w-3 text-primary" />
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  }

  if (variant === "compact") {
    return (
      <Button
        variant="outline"
        size="sm"
        className={`gap-2 ${className}`}
        onClick={() => {
          const currentIndex = routing.locales.indexOf(
            currentLocale as (typeof routing.locales)[number],
          );
          const nextIndex = (currentIndex + 1) % routing.locales.length;
          const nextLocale = routing.locales[nextIndex];
          handleLocaleChange(nextLocale);
        }}
        disabled={isPending}
      >
        <Languages className="h-4 w-4" />
        <span className="font-medium text-sm">
          {localeLabels[currentLocale as keyof typeof localeLabels]}
        </span>
      </Button>
    );
  }

  // Default dropdown variant
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={`gap-2 ${className}`}
          disabled={isPending}
        >
          <Globe className="h-4 w-4" />
          <span className="font-medium text-sm">
            {localeLabels[currentLocale as keyof typeof localeLabels]}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-[120px]">
        {routing.locales.map((locale) => (
          <DropdownMenuItem
            key={locale}
            onClick={() => handleLocaleChange(locale)}
            className="flex items-center justify-between"
          >
            <span className="font-medium text-sm">
              {localeLabels[locale as keyof typeof localeLabels]}
            </span>
            {locale === currentLocale && (
              <Check className="h-3 w-3 text-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
