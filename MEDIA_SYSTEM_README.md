# 媒体资源管理系统

## 概述

本系统为基于 Convex 的多语言新闻管理平台添加了完整的媒体资源管理功能。用户可以上传、管理和使用各种媒体文件，包括图片、视频、文档和音频文件。

## 功能特性

### 📁 文件管理
- **多格式支持**：图片(.jpg, .png, .gif, .webp)、视频(.mp4, .avi, .mov)、文档(.pdf, .doc, .txt)、音频(.mp3, .wav, .ogg)
- **拖拽上传**：支持拖拽文件到上传区域
- **批量上传**：一次选择多个文件同时上传
- **文件大小限制**：最大支持 50MB 的文件
- **自动分类**：根据文件类型自动归类

### 🔍 文件筛选和搜索
- **分类筛选**：按文件类型（图片、视频、文档、音频）筛选
- **关键词搜索**：根据文件名、描述、标签进行搜索
- **多视图模式**：网格视图和列表视图切换

### 📊 统计信息
- 总文件数量和总大小
- 各类型文件的统计数据
- 实时更新的统计面板

### 📝 文件元数据管理
- 文件描述和标签
- Alt 文本（用于图片）
- 访问次数统计
- 上传时间和最后访问时间

### 🗂️ 文件夹组织
- 创建和管理文件夹
- 文件移动和分组
- 颜色标识文件夹
- 嵌套文件夹支持

### ✨ 编辑器集成
- **增强编辑器**：在创建新闻时直接插入媒体文件
- **媒体选择器**：弹窗式媒体文件选择界面
- **图片插入**：直接在编辑器中显示图片
- **文件链接**：非图片文件自动生成下载链接

## 技术架构

### 后端 (Convex)
- **Schema 设计**：
  - `media_files`: 存储文件基本信息和元数据
  - `media_folders`: 文件夹管理
  - `media_file_folders`: 文件与文件夹的关联关系

- **API 接口**：
  - 文件上传和存储
  - 文件查询和搜索
  - 文件管理（更新、删除）
  - 文件夹管理
  - 统计信息

### 前端组件
- **MediaLibraryPage**: 主要的媒体库管理页面
- **MediaPicker**: 可重用的媒体选择器组件
- **EnhancedEditor**: 集成媒体插入功能的增强编辑器

### 国际化支持
- 中英文完整翻译
- 多语言界面适配

## 使用指南

### 1. 访问媒体库
在 Dashboard 导航菜单中点击 "媒体库" / "Media Library"

### 2. 上传文件
- **拖拽上传**：将文件拖拽到上传区域
- **点击上传**：点击上传区域选择文件
- **支持格式**：图片、视频、文档、音频等多种格式
- **大小限制**：单个文件最大 50MB

### 3. 管理文件
- **查看文件**：点击文件查看详情
- **编辑信息**：修改文件名、描述、标签等
- **复制链接**：获取文件的访问链接
- **删除文件**：永久删除不需要的文件

### 4. 在编辑器中使用
- 在创建新闻时，点击工具栏的图片图标
- 在媒体选择器中选择要插入的文件
- 图片将直接显示在编辑器中
- 其他文件类型会插入为下载链接

### 5. 文件组织
- **创建文件夹**：点击"创建文件夹"按钮
- **移动文件**：将文件拖拽到对应文件夹
- **文件夹颜色**：为文件夹设置颜色标识

## 开发说明

### 新增的文件结构
```
convex/
├── media.ts                 # 媒体管理 API
└── schema.ts               # 数据库 Schema 更新

src/
├── app/dashboard/media/
│   └── page.tsx            # 媒体库主页面
├── components/ui/
│   ├── media-picker.tsx    # 媒体选择器组件
│   └── enhanced-editor.tsx # 增强编辑器组件
└── messages/
    ├── en.json             # 英文翻译
    └── zh.json             # 中文翻译
```

### 主要依赖
```json
{
  "react-dropzone": "^14.2.3",
  "@tiptap/extension-image": "^2.1.13"
}
```

### API 使用示例
```typescript
// 获取媒体文件列表
const mediaFiles = useQuery(api.media.getMediaFiles, {
  category: "image",
  searchQuery: "photo",
  limit: 20,
});

// 上传文件
const generateUploadUrl = useMutation(api.media.generateUploadUrl);
const saveFileInfo = useMutation(api.media.saveFileInfo);

// 删除文件
const deleteMediaFile = useMutation(api.media.deleteMediaFile);
```

### 组件使用示例
```typescript
// 使用媒体选择器
import { MediaPicker } from "@/components/ui/media-picker";

<MediaPicker
  open={isOpen}
  onOpenChange={setIsOpen}
  onSelect={(file) => console.log("Selected:", file)}
  allowedTypes={["image", "video"]}
  multiple={false}
/>

// 使用增强编辑器
import { EnhancedEditor } from "@/components/ui/enhanced-editor";

<EnhancedEditor
  value={content}
  onChange={setContent}
  enableMediaPicker={true}
/>
```

## 扩展功能建议

### 短期改进
1. **图片编辑**：集成基础的图片裁剪和调整功能
2. **视频预览**：添加视频文件的缩略图预览
3. **批量操作**：支持批量删除、移动文件
4. **文件版本**：支持文件的版本管理

### 长期规划
1. **云存储集成**：支持 AWS S3、阿里云 OSS 等外部存储
2. **CDN 加速**：集成 CDN 服务提高文件访问速度
3. **权限管理**：精细化的文件访问权限控制
4. **API 管理**：为外部应用提供媒体文件 API

## 故障排除

### 常见问题
1. **上传失败**：检查文件大小是否超过限制，网络连接是否正常
2. **图片不显示**：确认文件 URL 可访问，检查跨域设置
3. **搜索无结果**：检查搜索关键词，确认文件元数据正确

### 性能优化
1. **懒加载**：大量文件时使用虚拟滚动
2. **缓存策略**：合理设置文件缓存时间
3. **压缩优化**：自动压缩大图片文件

## 安全考虑

1. **文件类型验证**：严格验证上传文件的类型和内容
2. **大小限制**：设置合理的文件大小限制
3. **访问控制**：确保文件访问权限的正确设置
4. **恶意文件检测**：集成病毒扫描和恶意文件检测

---

此媒体管理系统为多语言新闻平台提供了完整的资源管理解决方案，支持从文件上传到在编辑器中使用的完整工作流程。 