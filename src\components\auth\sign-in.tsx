"use client";

import { useAuthActions } from "@convex-dev/auth/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import type { AuthFlow } from "@/types/auth";

interface SignInProps {
  setState: (value: AuthFlow) => void;
}

export function SignIn({ setState }: SignInProps) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const { signIn } = useAuthActions();
  const t = useTranslations("auth.sign_in");

  const handleProviderSignIn = (value: "github" | "google") => {
    setLoading(true);
    signIn(value).finally(() => {
      setLoading(false);
    });
  };

  const handlePasswordSignIn = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    signIn("password", { email, password, flow: "signIn" }).finally(() => {
      setLoading(false);
    });
  };

  return (
    <Card className="w-full max-w-md rounded-2xl border-0 bg-white/80 shadow-2xl shadow-blue-500/20 backdrop-blur-sm">
      <CardHeader className="space-y-2 pb-6 text-center">
        <CardTitle className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text font-bold text-2xl text-transparent">
          {t("title")}
        </CardTitle>
        <CardDescription className="text-gray-600">
          {t("description")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <form onSubmit={handlePasswordSignIn} className="space-y-3">
          <Input
            disabled={loading}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder={t("email_placeholder")}
            type="email"
            required
          />
          <Input
            disabled={loading}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder={t("password_placeholder")}
            type="password"
            required
          />
          <Button
            className="group relative w-full overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 transition-all duration-300 hover:from-blue-700 hover:to-purple-700 hover:shadow-blue-500/25 hover:shadow-lg"
            size="lg"
            type="submit"
            disabled={loading}
          >
            <span className="relative z-10 flex items-center justify-center gap-2">
              {loading && (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white/20 border-t-white" />
              )}
              {t("continue_button")}
            </span>
            <div className="absolute inset-0 translate-x-[-100%] bg-gradient-to-r from-white/0 via-white/10 to-white/0 transition-transform duration-1000 group-hover:translate-x-[100%]" />
          </Button>
        </form>

        <Separator />

        <div className="space-y-2">
          <Button
            className="group relative w-full overflow-hidden border-gray-200/60 bg-white/50 text-gray-700 backdrop-blur-sm transition-all duration-300 hover:bg-white/80 hover:shadow-md"
            variant="outline"
            onClick={() => handleProviderSignIn("google")}
            disabled={loading}
            size="lg"
          >
            <span className="flex items-center justify-center gap-2">
              <svg className="h-5 w-5" viewBox="0 0 24 24" aria-label="Google">
                <title>Google</title>
                <path
                  fill="#4285f4"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="#34a853"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="#fbbc05"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="#ea4335"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              {t("google_button")}
            </span>
          </Button>
          <Button
            className="group relative w-full overflow-hidden border-gray-200/60 bg-white/50 text-gray-700 backdrop-blur-sm transition-all duration-300 hover:bg-white/80 hover:shadow-md"
            variant="outline"
            onClick={() => handleProviderSignIn("github")}
            disabled={loading}
            size="lg"
          >
            <span className="flex items-center justify-center gap-2">
              <svg
                className="h-5 w-5 fill-current"
                viewBox="0 0 24 24"
                aria-label="GitHub"
              >
                <title>GitHub</title>
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
              </svg>
              {t("github_button")}
            </span>
          </Button>
        </div>

        <div className="text-center text-muted-foreground text-sm">
          {t("no_account")}{" "}
          <button
            type="button"
            onClick={() => setState("signUp")}
            className="cursor-pointer underline hover:text-foreground"
          >
            {t("sign_up_link")}
          </button>
        </div>
      </CardContent>
    </Card>
  );
}
