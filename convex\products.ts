import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// 定义多语言文本类型
const multiLanguageText = v.record(v.string(), v.string()); // Record<languageCode, text>
const multiLanguageArray = v.record(v.string(), v.array(v.string())); // Record<languageCode, string[]>

/**
 * 创建产品
 */
export const createProduct = mutation({
  args: {
    name: multiLanguageText,
    title: v.optional(multiLanguageText),
    description: multiLanguageText,
    specifications: v.optional(multiLanguageText),
    instructions: v.optional(multiLanguageText),
    categoryId: v.id("products_categories"),
    coverImageId: v.optional(v.id("_storage")),
    imageIds: v.optional(v.array(v.id("_storage"))),
    seoKeywords: v.optional(multiLanguageArray),
    seoDescription: v.optional(multiLanguageText),
    slug: v.optional(multiLanguageText),
    sortOrder: v.optional(v.number()),
    isFeatured: v.optional(v.boolean()),
    originalLanguage: v.string(),
    availableLanguages: v.array(v.string()),
  },
  returns: v.id("products"),
  handler: async (ctx, args) => {
    const now = Date.now();
    return await ctx.db.insert("products", {
      ...args,
      status: "draft",
      publishedAt: now,
    });
  },
});

/**
 * 发布产品
 */
export const publishProduct = mutation({
  args: {
    productId: v.id("products"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new Error("Product not found");
    }

    await ctx.db.patch(args.productId, {
      status: "published",
      publishedAt: Date.now(),
    });
    return null;
  },
});

/**
 * 更新产品
 */
export const updateProduct = mutation({
  args: {
    productId: v.id("products"),
    name: v.optional(multiLanguageText),
    title: v.optional(multiLanguageText),
    description: v.optional(multiLanguageText),
    specifications: v.optional(multiLanguageText),
    instructions: v.optional(multiLanguageText),
    categoryId: v.optional(v.id("products_categories")),
    coverImageId: v.optional(v.id("_storage")),
    imageIds: v.optional(v.array(v.id("_storage"))),
    seoKeywords: v.optional(multiLanguageArray),
    seoDescription: v.optional(multiLanguageText),
    slug: v.optional(multiLanguageText),
    sortOrder: v.optional(v.number()),
    isFeatured: v.optional(v.boolean()),
    status: v.optional(
      v.union(
        v.literal("draft"),
        v.literal("published"),
        v.literal("discontinued"),
      ),
    ),
    availableLanguages: v.optional(v.array(v.string())),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { productId, ...updates } = args;
    const product = await ctx.db.get(productId);
    if (!product) {
      throw new Error("Product not found");
    }

    await ctx.db.patch(productId, updates);
    return null;
  },
});

/**
 * 删除产品
 */
export const deleteProduct = mutation({
  args: {
    productId: v.id("products"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new Error("Product not found");
    }

    // 先删除相关的标准
    const standards = await ctx.db
      .query("product_standards")
      .withIndex("by_product", (q) => q.eq("productId", args.productId))
      .collect();

    for (const standard of standards) {
      await ctx.db.delete(standard._id);
    }

    // 然后删除产品
    await ctx.db.delete(args.productId);
    return null;
  },
});

/**
 * 获取产品详情（包含标准）
 */
export const getProduct = query({
  args: {
    productId: v.id("products"),
    language: v.optional(v.string()), // 获取指定语言的内容
  },
  returns: v.union(
    v.object({
      _id: v.id("products"),
      _creationTime: v.number(),
      name: multiLanguageText,
      title: v.optional(multiLanguageText),
      description: multiLanguageText,
      specifications: v.optional(multiLanguageText),
      instructions: v.optional(multiLanguageText),
      coverImageId: v.optional(v.id("_storage")),
      categoryId: v.id("products_categories"),
      imageIds: v.optional(v.array(v.id("_storage"))),
      status: v.optional(
        v.union(
          v.literal("draft"),
          v.literal("published"),
          v.literal("discontinued"),
        ),
      ),
      seoKeywords: v.optional(multiLanguageArray),
      seoDescription: v.optional(multiLanguageText),
      slug: v.optional(multiLanguageText),
      sortOrder: v.optional(v.number()),
      isFeatured: v.optional(v.boolean()),
      publishedAt: v.optional(v.number()),
      originalLanguage: v.string(),
      availableLanguages: v.array(v.string()),
      category: v.object({
        _id: v.id("products_categories"),
        _creationTime: v.number(),
        name: multiLanguageText,
        description: multiLanguageText,
        sortOrder: v.optional(v.number()),
        isActive: v.optional(v.boolean()),
      }),
      standards: v.array(
        v.object({
          _id: v.id("product_standards"),
          standardType: multiLanguageText,
          standardValues: multiLanguageArray,
          sortOrder: v.optional(v.number()),
          originalLanguage: v.string(),
          availableLanguages: v.array(v.string()),
        }),
      ),
    }),
    v.null(),
  ),
  handler: async (ctx, args) => {
    const product = await ctx.db.get(args.productId);
    if (!product) {
      return null;
    }

    const category = await ctx.db.get(product.categoryId);
    if (!category) {
      throw new Error("Category not found");
    }

    // 获取产品标准
    const standards = await ctx.db
      .query("product_standards")
      .withIndex("by_product", (q) => q.eq("productId", args.productId))
      .filter((q) => q.neq(q.field("isActive"), false))
      .collect();

    // 按排序顺序排列标准
    const sortedStandards = standards.sort((a, b) => {
      const orderA = a.sortOrder ?? a._creationTime;
      const orderB = b.sortOrder ?? b._creationTime;
      return orderA - orderB;
    });

    return {
      ...product,
      category,
      standards: sortedStandards,
    };
  },
});

/**
 * 根据分类获取产品列表
 */
export const getProductsByCategory = query({
  args: {
    categoryId: v.id("products_categories"),
    language: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("products"),
      _creationTime: v.number(),
      name: multiLanguageText,
      title: v.optional(multiLanguageText),
      description: multiLanguageText,
      coverImageId: v.optional(v.id("_storage")),
      status: v.optional(
        v.union(
          v.literal("draft"),
          v.literal("published"),
          v.literal("discontinued"),
        ),
      ),
      isFeatured: v.optional(v.boolean()),
      sortOrder: v.optional(v.number()),
      originalLanguage: v.string(),
      availableLanguages: v.array(v.string()),
      category: v.object({
        _id: v.id("products_categories"),
        name: multiLanguageText,
        description: multiLanguageText,
      }),
    }),
  ),
  handler: async (ctx, args) => {
    const products = await ctx.db
      .query("products")
      .withIndex("by_category", (q) => q.eq("categoryId", args.categoryId))
      .order("desc")
      .take(args.limit || 20);

    const category = await ctx.db.get(args.categoryId);
    if (!category) {
      throw new Error("Category not found");
    }

    return products.map((product) => ({
      ...product,
      category: {
        _id: category._id,
        name: category.name,
        description: category.description,
      },
    }));
  },
});

/**
 * 根据状态获取产品列表
 */
export const getProductsByStatus = query({
  args: {
    status: v.union(
      v.literal("draft"),
      v.literal("published"),
      v.literal("discontinued"),
    ),
    language: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("products"),
      _creationTime: v.number(),
      name: multiLanguageText,
      title: v.optional(multiLanguageText),
      description: multiLanguageText,
      coverImageId: v.optional(v.id("_storage")),
      categoryId: v.id("products_categories"),
      status: v.optional(
        v.union(
          v.literal("draft"),
          v.literal("published"),
          v.literal("discontinued"),
        ),
      ),
      isFeatured: v.optional(v.boolean()),
      sortOrder: v.optional(v.number()),
      originalLanguage: v.string(),
      availableLanguages: v.array(v.string()),
      category: v.object({
        _id: v.id("products_categories"),
        name: multiLanguageText,
        description: multiLanguageText,
      }),
    }),
  ),
  handler: async (ctx, args) => {
    const products = await ctx.db
      .query("products")
      .withIndex("by_status", (q) => q.eq("status", args.status))
      .order("desc")
      .take(args.limit || 20);

    const productsWithCategory = await Promise.all(
      products.map(async (product) => {
        const category = await ctx.db.get(product.categoryId);
        if (!category) {
          throw new Error("Category not found");
        }
        return {
          ...product,
          category: {
            _id: category._id,
            name: category.name,
            description: category.description,
          },
        };
      }),
    );

    return productsWithCategory;
  },
});

/**
 * 获取所有产品列表
 */
export const getAllProducts = query({
  args: {
    language: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("products"),
      _creationTime: v.number(),
      name: multiLanguageText,
      title: v.optional(multiLanguageText),
      description: multiLanguageText,
      coverImageId: v.optional(v.id("_storage")),
      categoryId: v.id("products_categories"),
      status: v.optional(
        v.union(
          v.literal("draft"),
          v.literal("published"),
          v.literal("discontinued"),
        ),
      ),
      isFeatured: v.optional(v.boolean()),
      sortOrder: v.optional(v.number()),
      originalLanguage: v.string(),
      availableLanguages: v.array(v.string()),
      category: v.object({
        _id: v.id("products_categories"),
        name: multiLanguageText,
        description: multiLanguageText,
      }),
    }),
  ),
  handler: async (ctx, args) => {
    const products = await ctx.db
      .query("products")
      .order("desc")
      .take(args.limit || 50);

    const productsWithCategory = await Promise.all(
      products.map(async (product) => {
        const category = await ctx.db.get(product.categoryId);
        if (!category) {
          throw new Error("Category not found");
        }
        return {
          ...product,
          category: {
            _id: category._id,
            name: category.name,
            description: category.description,
          },
        };
      }),
    );

    return productsWithCategory;
  },
});

/**
 * 获取分类下的其他产品（排除当前产品）
 */
export const getRelatedProductsInCategory = query({
  args: {
    categoryId: v.id("products_categories"),
    excludeProductId: v.optional(v.id("products")),
    language: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("products"),
      name: multiLanguageText,
      title: v.optional(multiLanguageText),
      description: multiLanguageText,
      coverImageId: v.optional(v.id("_storage")),
      sortOrder: v.optional(v.number()),
      category: v.object({
        _id: v.id("products_categories"),
        name: multiLanguageText,
        description: multiLanguageText,
      }),
    }),
  ),
  handler: async (ctx, args) => {
    let products = await ctx.db
      .query("products")
      .withIndex("by_category", (q) => q.eq("categoryId", args.categoryId))
      .filter((q) => q.eq(q.field("status"), "published"))
      .order("desc")
      .take(args.limit ? args.limit + 1 : 21); // 多取一个以防排除当前产品后不够

    // 排除当前产品
    if (args.excludeProductId) {
      products = products.filter(
        (product) => product._id !== args.excludeProductId,
      );
    }

    // 限制数量
    if (args.limit) {
      products = products.slice(0, args.limit);
    }

    const category = await ctx.db.get(args.categoryId);
    if (!category) {
      throw new Error("Category not found");
    }

    return products.map((product) => ({
      _id: product._id,
      name: product.name,
      title: product.title,
      description: product.description,
      coverImageId: product.coverImageId,
      sortOrder: product.sortOrder,
      category: {
        _id: category._id,
        name: category.name,
        description: category.description,
      },
    }));
  },
});

/**
 * 获取推荐产品
 */
export const getFeaturedProducts = query({
  args: {
    language: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("products"),
      name: multiLanguageText,
      title: v.optional(multiLanguageText),
      description: multiLanguageText,
      coverImageId: v.optional(v.id("_storage")),
      sortOrder: v.optional(v.number()),
      category: v.object({
        _id: v.id("products_categories"),
        name: multiLanguageText,
        description: multiLanguageText,
      }),
    }),
  ),
  handler: async (ctx, args) => {
    const products = await ctx.db
      .query("products")
      .withIndex("by_featured", (q) => q.eq("isFeatured", true))
      .filter((q) => q.eq(q.field("status"), "published"))
      .order("desc")
      .take(args.limit || 10);

    const productsWithCategory = await Promise.all(
      products.map(async (product) => {
        const category = await ctx.db.get(product.categoryId);
        if (!category) {
          throw new Error("Category not found");
        }
        return {
          _id: product._id,
          name: product.name,
          title: product.title,
          description: product.description,
          coverImageId: product.coverImageId,
          sortOrder: product.sortOrder,
          category: {
            _id: category._id,
            name: category.name,
            description: category.description,
          },
        };
      }),
    );

    return productsWithCategory;
  },
});

/**
 * 搜索产品（支持多语言文本搜索）
 */
export const searchProducts = query({
  args: {
    searchTerm: v.string(),
    language: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("products"),
      name: multiLanguageText,
      title: v.optional(multiLanguageText),
      description: multiLanguageText,
      coverImageId: v.optional(v.id("_storage")),
      category: v.object({
        _id: v.id("products_categories"),
        name: multiLanguageText,
        description: multiLanguageText,
      }),
    }),
  ),
  handler: async (ctx, args) => {
    // 获取已发布的产品
    const products = await ctx.db
      .query("products")
      .withIndex("by_status", (q) => q.eq("status", "published"))
      .take(args.limit || 50);

    // 多语言文本搜索
    const searchTerm = args.searchTerm.toLowerCase();
    const targetLanguage = args.language || "zh"; // 默认中文

    const filteredProducts = products.filter((product) => {
      // 搜索名称
      const name =
        product.name[targetLanguage] || Object.values(product.name)[0] || "";
      if (name.toLowerCase().includes(searchTerm)) return true;

      // 搜索描述
      const description =
        product.description[targetLanguage] ||
        Object.values(product.description)[0] ||
        "";
      if (description.toLowerCase().includes(searchTerm)) return true;

      // 搜索标题
      if (product.title) {
        const title =
          product.title[targetLanguage] ||
          Object.values(product.title)[0] ||
          "";
        if (title.toLowerCase().includes(searchTerm)) return true;
      }

      // 搜索SEO关键词
      if (product.seoKeywords) {
        const keywords =
          product.seoKeywords[targetLanguage] ||
          Object.values(product.seoKeywords)[0] ||
          [];
        if (
          keywords.some((keyword) => keyword.toLowerCase().includes(searchTerm))
        )
          return true;
      }

      return false;
    });

    // 获取分类信息
    const productsWithCategory = await Promise.all(
      filteredProducts.map(async (product) => {
        const category = await ctx.db.get(product.categoryId);
        if (!category) {
          throw new Error("Category not found");
        }
        return {
          _id: product._id,
          name: product.name,
          title: product.title,
          description: product.description,
          coverImageId: product.coverImageId,
          category: {
            _id: category._id,
            name: category.name,
            description: category.description,
          },
        };
      }),
    );

    return productsWithCategory;
  },
});

/**
 * 基于标准获取相关产品（智能推荐）
 */
export const getSmartRelatedProducts = query({
  args: {
    productId: v.id("products"),
    language: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(
    v.object({
      _id: v.id("products"),
      name: multiLanguageText,
      title: v.optional(multiLanguageText),
      description: multiLanguageText,
      coverImageId: v.optional(v.id("_storage")),
      matchScore: v.number(), // 匹配分数
      sharedStandards: v.array(
        v.object({
          standardType: multiLanguageText,
          sharedValues: multiLanguageArray,
        }),
      ),
      category: v.object({
        _id: v.id("products_categories"),
        name: multiLanguageText,
        description: multiLanguageText,
      }),
    }),
  ),
  handler: async (ctx, args) => {
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new Error("Product not found");
    }

    const targetLanguage = args.language || "zh"; // 默认中文

    // 获取当前产品的标准
    const currentStandards = await ctx.db
      .query("product_standards")
      .withIndex("by_product", (q) => q.eq("productId", args.productId))
      .filter((q) => q.neq(q.field("isActive"), false))
      .collect();

    if (currentStandards.length === 0) {
      // 如果当前产品没有标准，回退到分类相关产品
      const categoryProducts = await ctx.db
        .query("products")
        .withIndex("by_category", (q) => q.eq("categoryId", product.categoryId))
        .filter((q) => q.eq(q.field("status"), "published"))
        .filter((q) => q.neq(q.field("_id"), args.productId))
        .take(args.limit || 5);

      const category = await ctx.db.get(product.categoryId);
      if (!category) {
        throw new Error("Category not found");
      }

      return categoryProducts.map((p) => ({
        _id: p._id,
        name: p.name,
        title: p.title,
        description: p.description,
        coverImageId: p.coverImageId,
        matchScore: 0.5, // 基础分数，因为同分类
        sharedStandards: [],
        category: {
          _id: category._id,
          name: category.name,
          description: category.description,
        },
      }));
    }

    // 获取所有其他已发布的产品
    const allProducts = await ctx.db
      .query("products")
      .filter((q) => q.eq(q.field("status"), "published"))
      .filter((q) => q.neq(q.field("_id"), args.productId))
      .collect();

    // 计算每个产品的相似度
    const productMatches = await Promise.all(
      allProducts.map(async (otherProduct) => {
        const otherStandards = await ctx.db
          .query("product_standards")
          .withIndex("by_product", (q) => q.eq("productId", otherProduct._id))
          .filter((q) => q.neq(q.field("isActive"), false))
          .collect();

        // 计算标准匹配度
        let totalMatches = 0;
        let totalStandards = 0;
        const sharedStandards: Array<{
          standardType: Record<string, string>;
          sharedValues: Record<string, string[]>;
        }> = [];

        for (const currentStd of currentStandards) {
          const matchingStd = otherStandards.find((s) => {
            const currentType =
              currentStd.standardType[targetLanguage] ||
              Object.values(currentStd.standardType)[0];
            const otherType =
              s.standardType[targetLanguage] ||
              Object.values(s.standardType)[0];
            return currentType === otherType;
          });

          if (matchingStd) {
            // 找到共同的标准值
            const currentValues =
              currentStd.standardValues[targetLanguage] ||
              Object.values(currentStd.standardValues)[0] ||
              [];
            const matchingValues =
              matchingStd.standardValues[targetLanguage] ||
              Object.values(matchingStd.standardValues)[0] ||
              [];

            const sharedValues = currentValues.filter((value) =>
              matchingValues.includes(value),
            );

            if (sharedValues.length > 0) {
              const sharedValuesRecord: Record<string, string[]> = {};
              sharedValuesRecord[targetLanguage] = sharedValues;

              sharedStandards.push({
                standardType: currentStd.standardType,
                sharedValues: sharedValuesRecord,
              });

              // 计算匹配分数
              const matchRatio = sharedValues.length / currentValues.length;
              totalMatches += matchRatio;
            }
          }
          totalStandards++;
        }

        let matchScore = totalStandards > 0 ? totalMatches / totalStandards : 0;

        // 如果是同分类，增加额外分数
        if (otherProduct.categoryId === product.categoryId) {
          matchScore += 0.2;
        }

        const category = await ctx.db.get(otherProduct.categoryId);
        if (!category) {
          throw new Error("Category not found");
        }

        return {
          _id: otherProduct._id,
          name: otherProduct.name,
          title: otherProduct.title,
          description: otherProduct.description,
          coverImageId: otherProduct.coverImageId,
          matchScore,
          sharedStandards,
          category: {
            _id: category._id,
            name: category.name,
            description: category.description,
          },
        };
      }),
    );

    // 按匹配分数排序，只返回有匹配的产品
    const filteredMatches = productMatches
      .filter((match) => match.matchScore > 0.1) // 最低匹配阈值
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, args.limit || 5);

    return filteredMatches;
  },
});
