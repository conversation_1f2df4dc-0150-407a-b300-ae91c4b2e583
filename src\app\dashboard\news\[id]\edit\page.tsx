"use client";

import { api } from "@convex/api";
import { useMutation, useQuery } from "convex/react";
import {
  AlertCircle,
  ArrowLeft,
  Calendar,
  Check,
  Clock,
  Eye,
  FileText,
  Globe,
  ImageIcon,
  Layers,
  PenTool,
  Plus,
  Save,
  Settings,
  Tag,
  Users,
  X,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CoverImagePicker } from "@/components/ui/cover-image-picker";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import {
  EnhancedEditor,
  type EnhancedEditorRef,
} from "@/components/ui/enhanced-editor";
import { SUPPORTED_LANGUAGES } from "@/constants/languages";

type MultiLanguageText = Record<string, string>;
type MultiLanguageArray = Record<string, string[]>;

export default function EditNewsPage() {
  const params = useParams();
  const router = useRouter();
  const t = useTranslations("news");
  const locale = useLocale();
  const editorRef = useRef<EnhancedEditorRef>(null);

  const articleId = params.id as string;

  // Validate the articleId format - Convex IDs have a specific format
  const isValidId =
    articleId && articleId.length > 10 && !articleId.includes("categories");

  // Form state
  const [title, setTitle] = useState<MultiLanguageText>({});
  const [content, setContent] = useState<MultiLanguageText>({});
  const [summary, setSummary] = useState<MultiLanguageText>({});
  const [categoryId, setCategoryId] = useState<string>("");
  const [coverImageId, setCoverImageId] = useState<string | undefined>();
  const [seoKeywords, setSeoKeywords] = useState<MultiLanguageArray>({});
  const [seoDescription, setSeoDescription] = useState<MultiLanguageText>({});
  const [slug, setSlug] = useState<MultiLanguageText>({});
  const [originalLanguage, setOriginalLanguage] = useState<string>(locale);
  const [availableLanguages, setAvailableLanguages] = useState<string[]>([
    locale,
  ]);
  const [activeLanguage, setActiveLanguage] = useState<string>(locale);
  const [status, setStatus] = useState<"draft" | "published" | "archived">(
    "draft",
  );

  // Loading states
  const [isSaving, setIsSaving] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  // Queries and mutations
  const article = useQuery(
    api.news.getNewsArticleWithAllLanguages,
    isValidId
      ? {
          // biome-ignore lint/suspicious/noExplicitAny: Convex ID type conversion
          articleId: articleId as any,
        }
      : "skip",
  );

  const updateArticle = useMutation(api.news.updateNewsArticle);
  const categories = useQuery(api.news.getNewsCategories, {
    includeInactive: false,
    language: activeLanguage,
  });

  // 查询封面图信息用于预览
  const coverImageInfo = useQuery(
    api.media.getMediaFileByStorageId,
    coverImageId ? { storageId: coverImageId as any } : "skip",
  );

  // Load article data when available
  useEffect(() => {
    if (article && !isLoaded) {
      console.log("Loading article data:", article);

      // Use the existing multilingual data from the article
      setTitle(article.title || {});
      setContent(article.content || {});
      setSummary(article.summary || {});
      setSeoDescription(article.seoDescription || {});
      setSlug(article.slug || {});
      setSeoKeywords(article.seoKeywords || {});
      setCategoryId(article.categoryId);
      setCoverImageId(article.coverImageId);
      setOriginalLanguage(article.originalLanguage);
      setAvailableLanguages(article.availableLanguages);
      setActiveLanguage(article.originalLanguage);
      setStatus(article.status || "draft");
      setIsLoaded(true);

      console.log("Article data loaded successfully");
    }
  }, [article, isLoaded]);

  // Helper functions
  const addLanguage = (langCode: string) => {
    if (!availableLanguages.includes(langCode)) {
      setAvailableLanguages([...availableLanguages, langCode]);
      setActiveLanguage(langCode);
    }
  };

  const removeLanguage = (langCode: string) => {
    if (langCode === originalLanguage) return; // Cannot remove original language
    setAvailableLanguages(
      availableLanguages.filter((lang) => lang !== langCode),
    );

    // Clean up data for removed language
    const { [langCode]: _, ...restTitle } = title;
    const { [langCode]: __, ...restContent } = content;
    const { [langCode]: ___, ...restSummary } = summary;
    const { [langCode]: ____, ...restSeoDesc } = seoDescription;
    const { [langCode]: _____, ...restSlug } = slug;
    const { [langCode]: ______, ...restKeywords } = seoKeywords;

    setTitle(restTitle);
    setContent(restContent);
    setSummary(restSummary);
    setSeoDescription(restSeoDesc);
    setSlug(restSlug);
    setSeoKeywords(restKeywords);

    if (activeLanguage === langCode) {
      setActiveLanguage(originalLanguage);
    }
  };

  const updateTitle = (value: string) => {
    setTitle({ ...title, [activeLanguage]: value });
  };

  const updateContent = (value: string) => {
    setContent({ ...content, [activeLanguage]: value });
  };

  const updateSummary = (value: string) => {
    setSummary({ ...summary, [activeLanguage]: value });
  };

  const updateSeoDescription = (value: string) => {
    setSeoDescription({ ...seoDescription, [activeLanguage]: value });
  };

  const updateSlug = (value: string) => {
    setSlug({ ...slug, [activeLanguage]: value });
  };

  const updateSeoKeywords = (value: string) => {
    const keywords = value
      .split(",")
      .map((k) => k.trim())
      .filter((k) => k);
    setSeoKeywords({ ...seoKeywords, [activeLanguage]: keywords });
  };

  // 从内容中提取第一张图片
  const extractCoverFromContent = (): string | null => {
    const htmlContent =
      content[activeLanguage] || content[originalLanguage] || "";

    // 解析 HTML 内容，寻找图片标签
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, "text/html");
    const images = doc.querySelectorAll("img");

    for (const img of images) {
      const src = img.getAttribute("src");
      if (src?.includes("convex.cloud/api/storage/")) {
        return src; // 返回完整的URL
      }
    }

    return null;
  };

  const handleSave = async (publishNow = false) => {
    console.log("handleSave called with publishNow:", publishNow);

    if (!categoryId) {
      toast.error(t("shared.errors.select_category"));
      return;
    }

    if (!title[originalLanguage]) {
      toast.error(t("shared.errors.fill_title"));
      return;
    }

    if (!content[originalLanguage]) {
      toast.error(t("shared.errors.fill_content"));
      return;
    }

    setIsSaving(true);
    toast.loading(
      publishNow ? t("shared.errors.publishing") : t("shared.errors.updating"),
    );

    try {
      console.log("Updating article with data:", {
        title,
        content,
        summary,
        categoryId,
        availableLanguages,
        status: publishNow ? "published" : status,
      });

      await updateArticle({
        // biome-ignore lint/suspicious/noExplicitAny: Convex ID type conversion
        articleId: articleId as any,
        title,
        content,
        summary: Object.keys(summary).length > 0 ? summary : undefined,
        // biome-ignore lint/suspicious/noExplicitAny: Convex ID type conversion
        categoryId: categoryId as any,
        coverImageId: coverImageId as any,
        seoKeywords:
          Object.keys(seoKeywords).length > 0 ? seoKeywords : undefined,
        seoDescription:
          Object.keys(seoDescription).length > 0 ? seoDescription : undefined,
        slug: Object.keys(slug).length > 0 ? slug : undefined,
        availableLanguages,
        status: publishNow ? "published" : status,
      });

      console.log("Article updated successfully");

      // 如果选择立即发布且当前不是已发布状态，则发布
      if (publishNow && status !== "published") {
        setStatus("published");
      }

      toast.dismiss();
      toast.success(
        publishNow
          ? t("shared.errors.published_success")
          : t("shared.errors.updated_success"),
      );
      router.push(`/dashboard/news/${articleId}`);
    } catch (error) {
      console.error("Save failed:", error);
      toast.dismiss();

      // More detailed error handling
      if (error instanceof Error) {
        toast.error(t("shared.errors.save_failed", { message: error.message }));
      } else {
        toast.error(t("shared.errors.save_failed_generic"));
      }
    } finally {
      setIsSaving(false);
    }
  };

  const getCurrentLanguageData = () => ({
    title: title[activeLanguage] || "",
    content: content[activeLanguage] || "",
    summary: summary[activeLanguage] || "",
    seoDescription: seoDescription[activeLanguage] || "",
    slug: slug[activeLanguage] || "",
    seoKeywords: (seoKeywords[activeLanguage] || []).join(", "),
  });

  // Handle invalid IDs
  if (!isValidId) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="text-center">
            <h2 className="mb-2 font-semibold text-2xl">
              {t("shared.errors.page_not_found")}
            </h2>
            <p className="mb-4 text-muted-foreground">
              {t("shared.errors.article_not_found")}
            </p>
            <Link href="/dashboard/news">
              <Button>
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t("shared.errors.back_to_news")}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!article || !isLoaded) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="text-center">
            <h2 className="mb-2 font-semibold text-2xl">
              {t("shared.errors.loading")}
            </h2>
            <p className="text-muted-foreground">
              {t("shared.errors.loading_content")}
            </p>
          </div>
        </div>
      </div>
    );
  }

  const currentData = getCurrentLanguageData();
  const activeLanguageInfo = SUPPORTED_LANGUAGES.find(
    (lang) => lang.code === activeLanguage,
  );

  // Calculate completion progress
  const getCompletionProgress = () => {
    let completed = 0;
    const total = 6;

    if (title[activeLanguage]) completed++;
    if (content[activeLanguage]) completed++;
    if (summary[activeLanguage]) completed++;
    if (categoryId) completed++;
    if (slug[activeLanguage]) completed++;
    if (seoDescription[activeLanguage]) completed++;

    return Math.round((completed / total) * 100);
  };

  const completionProgress = getCompletionProgress();

  return (
    <div className="space-y-6">
      {/* Header Actions - Responsive Layout */}
      <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
        {/* Left Section - Title and Navigation */}
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          <Link href={`/dashboard/news/${articleId}`}>
            <Button variant="ghost" size="sm" className="gap-2 self-start">
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden sm:inline">
                {t("shared.ui.back_to_detail")}
              </span>
              <span className="sm:hidden">{t("shared.ui.back")}</span>
            </Button>
          </Link>

          <div className="hidden h-6 w-px bg-gray-300 sm:block" />

          <div>
            <h1 className="font-bold text-gray-900 text-xl sm:text-2xl">
              {t("shared.ui.edit_article")}
            </h1>
            <p className="text-gray-600 text-sm">
              {t("shared.ui.currently_editing")}
              {activeLanguageInfo?.nativeName} {activeLanguageInfo?.flag}
            </p>
          </div>
        </div>

        {/* Right Section - Progress and Actions */}
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          {/* Progress Indicator */}
          <div className="flex items-center justify-between text-gray-600 text-sm sm:justify-start sm:space-x-2">
            <span className="sm:hidden">
              {t("shared.ui.completion_progress")}
            </span>
            <div className="flex items-center space-x-2">
              <Progress
                value={completionProgress}
                className="h-2 w-16 sm:w-20"
              />
              <span className="min-w-12 font-medium">
                {completionProgress}%
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col gap-2 sm:flex-row sm:space-x-3">
            <Button
              variant="outline"
              onClick={() => handleSave(false)}
              disabled={isSaving}
              className="order-2 gap-2 sm:order-1"
              size="sm"
            >
              <Save className="h-4 w-4" />
              <span className="hidden sm:inline">
                {t("shared.ui.save_changes")}
              </span>
              <span className="sm:hidden">{t("shared.ui.save")}</span>
            </Button>
            {status !== "published" && (
              <Button
                onClick={() => handleSave(true)}
                disabled={isSaving}
                className="order-1 gap-2 bg-green-600 hover:bg-green-700 sm:order-2"
                size="sm"
              >
                <Eye className="h-4 w-4" />
                <span className="hidden sm:inline">
                  {t("shared.ui.publish_article")}
                </span>
                <span className="sm:hidden">{t("shared.ui.publish")}</span>
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 gap-6 lg:gap-8 xl:grid-cols-12">
        {/* Main Content */}
        <div className="space-y-6 xl:col-span-8">
          {/* Language Tabs */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="flex items-center space-x-3">
                  <div className="rounded-lg bg-blue-100 p-2">
                    <Globe className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">
                      {t("shared.ui.multilang_management")}
                    </CardTitle>
                    <p className="text-gray-600 text-sm">
                      {t("shared.ui.multilang_description")}
                    </p>
                  </div>
                </div>
                <Select
                  value=""
                  onValueChange={(value) => value && addLanguage(value)}
                >
                  <SelectTrigger className="w-full sm:w-48">
                    <Plus className="mr-2 h-4 w-4" />
                    <SelectValue placeholder={t("shared.ui.add_language")} />
                  </SelectTrigger>
                  <SelectContent>
                    {SUPPORTED_LANGUAGES.filter(
                      (lang) => !availableLanguages.includes(lang.code),
                    ).map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        <span className="flex items-center gap-2">
                          <span>{lang.flag}</span>
                          <span>{lang.nativeName}</span>
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {availableLanguages.map((langCode) => {
                  const lang = SUPPORTED_LANGUAGES.find(
                    (l) => l.code === langCode,
                  );
                  const isOriginal = langCode === originalLanguage;
                  const isActive = langCode === activeLanguage;

                  return (
                    <Badge
                      key={langCode}
                      variant={isActive ? "default" : "secondary"}
                      className={`relative cursor-pointer px-3 py-2 text-sm ${
                        isOriginal ? "ring-2 ring-blue-500" : ""
                      } ${isActive ? "bg-blue-600" : ""}`}
                      onClick={() => setActiveLanguage(langCode)}
                    >
                      <span className="flex items-center gap-2">
                        <span>{lang?.flag}</span>
                        <span>{lang?.nativeName}</span>
                        {isOriginal && (
                          <span className="rounded bg-white/20 px-1 text-xs">
                            {t("shared.ui.original_badge")}
                          </span>
                        )}
                      </span>
                      {!isOriginal && (
                        <button
                          type="button"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeLanguage(langCode);
                          }}
                          className="ml-2 rounded-full p-0.5 hover:bg-red-500/20"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      )}
                    </Badge>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Cover Image Selection */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="rounded-lg bg-indigo-100 p-2">
                  <ImageIcon className="h-5 w-5 text-indigo-600" />
                </div>
                <div>
                  <span>{t("shared.ui.cover_image")}</span>
                  <p className="font-normal text-gray-600 text-sm">
                    {t("shared.ui.cover_image_description")}
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CoverImagePicker
                value={coverImageId}
                onChange={setCoverImageId}
                extractFromContent={extractCoverFromContent}
              />
            </CardContent>
          </Card>

          {/* Content Editor */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="rounded-lg bg-green-100 p-2">
                  <PenTool className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <span>{t("shared.ui.article_content")}</span>
                  <p className="font-normal text-gray-600 text-sm">
                    {activeLanguageInfo?.nativeName} {t("shared.ui.version")}
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="title" className="font-medium text-base">
                  {t("shared.ui.article_title")}
                </Label>
                <Input
                  id="title"
                  value={currentData.title}
                  onChange={(e) => updateTitle(e.target.value)}
                  placeholder={t("shared.ui.article_title_placeholder")}
                  className="mt-2 h-12 font-medium text-lg"
                />
              </div>

              <div>
                <Label htmlFor="summary" className="font-medium text-base">
                  {t("shared.ui.article_summary")}
                </Label>
                <Textarea
                  id="summary"
                  value={currentData.summary}
                  onChange={(e) => updateSummary(e.target.value)}
                  placeholder={t("shared.ui.article_summary_placeholder")}
                  rows={3}
                  className="mt-2"
                />
              </div>

              <div>
                <Label className="font-medium text-base">
                  {t("shared.ui.article_body")}
                </Label>
                <div className="mt-2">
                  <EnhancedEditor
                    ref={editorRef}
                    value={currentData.content}
                    onChange={(value: string) => updateContent(value)}
                    placeholder={t("shared.ui.article_body_placeholder")}
                    className="min-h-[400px] sm:min-h-[500px]"
                    enableMediaPicker={true}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* SEO Settings */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="rounded-lg bg-purple-100 p-2">
                  <Settings className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <span>{t("shared.ui.seo_optimization")}</span>
                  <p className="font-normal text-gray-600 text-sm">
                    {t("shared.ui.seo_description")}
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="slug" className="font-medium text-base">
                  {t("shared.ui.url_slug")}
                </Label>
                <Input
                  id="slug"
                  value={currentData.slug}
                  onChange={(e) => updateSlug(e.target.value)}
                  placeholder={t("shared.ui.url_slug_placeholder")}
                  className="mt-2"
                />
                <p className="mt-1 text-gray-500 text-xs">
                  {t("shared.ui.url_slug_help")}
                </p>
              </div>

              <div>
                <Label
                  htmlFor="seo-description"
                  className="font-medium text-base"
                >
                  {t("shared.ui.seo_description_label")}
                </Label>
                <Textarea
                  id="seo-description"
                  value={currentData.seoDescription}
                  onChange={(e) => updateSeoDescription(e.target.value)}
                  placeholder={t("shared.ui.seo_description_placeholder")}
                  rows={3}
                  className="mt-2"
                />
                <p className="mt-1 text-gray-500 text-xs">
                  {t("shared.ui.seo_description_help")}
                </p>
              </div>

              <div>
                <Label htmlFor="seo-keywords" className="font-medium text-base">
                  {t("shared.ui.keywords")}
                </Label>
                <Input
                  id="seo-keywords"
                  value={currentData.seoKeywords}
                  onChange={(e) => updateSeoKeywords(e.target.value)}
                  placeholder={t("shared.ui.keywords_placeholder")}
                  className="mt-2"
                />
                <p className="mt-1 text-gray-500 text-xs">
                  {t("shared.ui.keywords_help")}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6 xl:col-span-4">
          {/* Publishing Options */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="rounded-lg bg-orange-100 p-2">
                  <Layers className="h-5 w-5 text-orange-600" />
                </div>
                <span>{t("shared.ui.publish_settings")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="category" className="font-medium text-base">
                  {t("shared.ui.category")}
                </Label>
                <Select value={categoryId} onValueChange={setCategoryId}>
                  <SelectTrigger className="mt-2">
                    <SelectValue placeholder={t("shared.ui.select_category")} />
                  </SelectTrigger>
                  <SelectContent>
                    {categories?.map((category) => (
                      <SelectItem key={category._id} value={category._id}>
                        <span className="flex items-center gap-2">
                          <Tag className="h-4 w-4" />
                          {category.name}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div>
                <Label htmlFor="status" className="font-medium text-base">
                  {t("shared.ui.article_status")}
                </Label>
                <Select
                  value={status}
                  onValueChange={(value: "draft" | "published" | "archived") =>
                    setStatus(value)
                  }
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">
                      <span className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-yellow-500" />
                        {t("status.draft")}
                      </span>
                    </SelectItem>
                    <SelectItem value="published">
                      <span className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-green-500" />
                        {t("status.published")}
                      </span>
                    </SelectItem>
                    <SelectItem value="archived">
                      <span className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-gray-500" />
                        {t("status.archived")}
                      </span>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div>
                <Label
                  htmlFor="original-language"
                  className="font-medium text-base"
                >
                  {t("shared.ui.original_language")}
                </Label>
                <Select
                  value={originalLanguage}
                  onValueChange={setOriginalLanguage}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {SUPPORTED_LANGUAGES.map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        <span className="flex items-center gap-2">
                          <span>{lang.flag}</span>
                          <span>{lang.nativeName}</span>
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Preview */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="rounded-lg bg-pink-100 p-2">
                  <Eye className="h-5 w-5 text-pink-600" />
                </div>
                <span>{t("shared.ui.preview")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex aspect-video items-center justify-center rounded-lg bg-gray-100">
                  {coverImageInfo?.url ? (
                    <Image
                      src={coverImageInfo.url}
                      alt="封面图预览"
                      width={300}
                      height={200}
                      className="h-full w-full rounded-lg object-cover"
                    />
                  ) : (
                    <FileText className="h-8 w-8 text-gray-400" />
                  )}
                </div>
                <h3 className="line-clamp-2 font-semibold text-lg">
                  {currentData.title || t("shared.ui.no_title")}
                </h3>
                <p className="line-clamp-3 text-gray-600 text-sm">
                  {currentData.summary || t("shared.ui.no_summary")}
                </p>
                <div className="flex flex-wrap items-center gap-2 text-gray-500 text-xs sm:flex-nowrap sm:space-x-4">
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-3 w-3" />
                    <span>{new Date().toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <span>
                      {t("shared.ui.reading_time", {
                        minutes: Math.ceil(
                          (currentData.content?.length || 0) / 400,
                        ),
                      })}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Completion Status */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="rounded-lg bg-emerald-100 p-2">
                  <Users className="h-5 w-5 text-emerald-600" />
                </div>
                <span>{t("shared.ui.completion_status")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 text-sm">
                    {t("shared.ui.overall_progress")}
                  </span>
                  <span className="font-medium text-sm">
                    {completionProgress}%
                  </span>
                </div>
                <Progress value={completionProgress} className="h-2" />

                <div className="grid grid-cols-1 gap-2 text-xs sm:grid-cols-2 lg:grid-cols-1">
                  <div className="flex items-center space-x-2">
                    {title[activeLanguage] ? (
                      <Check className="h-3 w-3 flex-shrink-0 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 flex-shrink-0 text-gray-400" />
                    )}
                    <span
                      className={
                        title[activeLanguage]
                          ? "text-green-600"
                          : "text-gray-500"
                      }
                    >
                      {t("shared.ui.article_title")}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {content[activeLanguage] ? (
                      <Check className="h-3 w-3 flex-shrink-0 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 flex-shrink-0 text-gray-400" />
                    )}
                    <span
                      className={
                        content[activeLanguage]
                          ? "text-green-600"
                          : "text-gray-500"
                      }
                    >
                      {t("shared.ui.article_content")}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {summary[activeLanguage] ? (
                      <Check className="h-3 w-3 flex-shrink-0 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 flex-shrink-0 text-gray-400" />
                    )}
                    <span
                      className={
                        summary[activeLanguage]
                          ? "text-green-600"
                          : "text-gray-500"
                      }
                    >
                      {t("shared.ui.article_summary")}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {categoryId ? (
                      <Check className="h-3 w-3 flex-shrink-0 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 flex-shrink-0 text-gray-400" />
                    )}
                    <span
                      className={
                        categoryId ? "text-green-600" : "text-gray-500"
                      }
                    >
                      {t("shared.ui.category")}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {slug[activeLanguage] ? (
                      <Check className="h-3 w-3 flex-shrink-0 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 flex-shrink-0 text-gray-400" />
                    )}
                    <span
                      className={
                        slug[activeLanguage]
                          ? "text-green-600"
                          : "text-gray-500"
                      }
                    >
                      {t("shared.ui.url_slug")}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {seoDescription[activeLanguage] ? (
                      <Check className="h-3 w-3 flex-shrink-0 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 flex-shrink-0 text-gray-400" />
                    )}
                    <span
                      className={
                        seoDescription[activeLanguage]
                          ? "text-green-600"
                          : "text-gray-500"
                      }
                    >
                      {t("shared.ui.seo_description_label")}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
