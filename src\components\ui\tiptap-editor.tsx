"use client";

import Placeholder from "@tiptap/extension-placeholder";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>u, EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import {
  Bold,
  Code,
  Heading1,
  Heading2,
  Heading3,
  Italic,
  List,
  ListOrdered,
  Minus,
  Quote,
  Redo,
  Strikethrough,
  Type,
  Undo,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { forwardRef, useEffect, useImperativeHandle } from "react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface TiptapEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  editable?: boolean;
  showToolbar?: boolean;
}

export interface TiptapEditorRef {
  getHTML: () => string;
  setContent: (content: string) => void;
  focus: () => void;
  clear: () => void;
}

const TiptapEditor = forwardRef<TiptapEditorRef, TiptapEditorProps>(
  (
    {
      value = "",
      onChange,
      placeholder,
      className,
      editable = true,
      showToolbar = true,
    },
    ref,
  ) => {
    const t = useTranslations("editor");
    const defaultPlaceholder = placeholder || t("placeholder");

    const editor = useEditor({
      extensions: [
        StarterKit.configure({
          bulletList: {
            keepMarks: true,
            keepAttributes: false,
          },
          orderedList: {
            keepMarks: true,
            keepAttributes: false,
          },
        }),
        Placeholder.configure({
          placeholder: defaultPlaceholder,
        }),
      ],
      content: value,
      editable,
      onUpdate: ({ editor }) => {
        const html = editor.getHTML();
        if (onChange) {
          onChange(html);
        }
      },
      editorProps: {
        attributes: {
          class: cn(
            // 基础样式
            "mx-auto max-w-none focus:outline-none",
            "text-gray-900 text-sm leading-relaxed",
            "min-h-[200px] px-6 py-4",
            // 标题样式
            "[&_h1]:mt-6 [&_h1]:mb-4 [&_h1]:font-bold [&_h1]:text-2xl [&_h1]:text-gray-900 [&_h1]:leading-tight",
            "[&_h2]:mt-5 [&_h2]:mb-3 [&_h2]:font-bold [&_h2]:text-gray-900 [&_h2]:text-xl [&_h2]:leading-tight",
            "[&_h3]:mt-4 [&_h3]:mb-2 [&_h3]:font-bold [&_h3]:text-gray-900 [&_h3]:text-lg [&_h3]:leading-tight",
            // 段落样式
            "[&_p]:mb-4 [&_p]:text-gray-700 [&_p]:leading-relaxed",
            "[&_p:first-child]:mt-0 [&_p:last-child]:mb-0",
            // 链接样式
            "[&_a]:text-blue-600 [&_a]:no-underline hover:[&_a]:underline",
            // 强调样式
            "[&_strong]:font-semibold [&_strong]:text-gray-900",
            "[&_em]:italic",
            "[&_s]:text-gray-500 [&_s]:line-through",
            // 代码样式
            "[&_code]:rounded [&_code]:bg-red-50 [&_code]:px-1 [&_code]:py-0.5 [&_code]:font-mono [&_code]:text-red-600 [&_code]:text-sm",
            "[&_pre]:overflow-x-auto [&_pre]:rounded [&_pre]:bg-gray-900 [&_pre]:p-3 [&_pre]:font-mono [&_pre]:text-gray-100 [&_pre]:text-sm",
            "[&_pre_code]:bg-transparent [&_pre_code]:p-0 [&_pre_code]:text-gray-100",
            // 引用样式
            "[&_blockquote]:my-4 [&_blockquote]:border-gray-300 [&_blockquote]:border-l-4 [&_blockquote]:py-2 [&_blockquote]:pl-4 [&_blockquote]:text-gray-600 [&_blockquote]:italic",
            // 列表样式
            "[&_ul]:mb-4 [&_ul]:list-outside [&_ul]:list-disc [&_ul]:space-y-1 [&_ul]:pl-6",
            "[&_ol]:mb-4 [&_ol]:list-outside [&_ol]:list-decimal [&_ol]:space-y-1 [&_ol]:pl-6",
            "[&_li]:pl-1 [&_li]:text-gray-700 [&_li]:leading-relaxed",
            "[&_ul_ul]:mt-2 [&_ul_ul]:mb-0 [&_ul_ul]:ml-4 [&_ul_ul]:pl-4",
            "[&_ol_ol]:mt-2 [&_ol_ol]:mb-0 [&_ol_ol]:ml-4 [&_ol_ol]:pl-4",
            // 分隔线样式
            "[&_hr]:my-6 [&_hr]:border-0 [&_hr]:border-gray-300 [&_hr]:border-t",
          ),
        },
      },
    });

    // 同步外部 value 变化到编辑器
    useEffect(() => {
      if (editor && value !== undefined) {
        const currentContent = editor.getHTML();
        if (currentContent !== value) {
          editor.commands.setContent(value, false);
        }
      }
    }, [editor, value]);

    useImperativeHandle(ref, () => ({
      getHTML: () => editor?.getHTML() || "",
      setContent: (content: string) => {
        if (editor) {
          editor.commands.setContent(content, false);
        }
      },
      focus: () => {
        if (editor) {
          editor.commands.focus();
        }
      },
      clear: () => {
        if (editor) {
          editor.commands.clearContent();
        }
      },
    }));

    if (!editor) {
      return null;
    }

    const ToolbarButton = ({
      onClick,
      disabled,
      isActive,
      children,
      title,
    }: {
      onClick: () => void;
      disabled?: boolean;
      isActive?: boolean;
      children: React.ReactNode;
      title?: string;
    }) => (
      <Button
        size="sm"
        variant={isActive ? "default" : "ghost"}
        onMouseDown={(e) => {
          e.preventDefault(); // 防止失去焦点
        }}
        onClick={(e) => {
          e.preventDefault();
          onClick();
        }}
        disabled={disabled}
        title={title}
        className="h-8 w-8 p-0"
      >
        {children}
      </Button>
    );

    return (
      <div
        className={cn(
          "overflow-hidden rounded-lg border border-gray-200 bg-white",
          className,
        )}
      >
        {showToolbar && (
          <div className="border-gray-200 border-b bg-gray-50 p-2">
            <div className="flex flex-wrap items-center gap-1">
              {/* Text Formatting */}
              <ToolbarButton
                onClick={() => editor.chain().focus().toggleBold().run()}
                disabled={!editor.can().chain().focus().toggleBold().run()}
                isActive={editor.isActive("bold")}
                title={t("toolbar.bold")}
              >
                <Bold className="h-4 w-4" />
              </ToolbarButton>

              <ToolbarButton
                onClick={() => editor.chain().focus().toggleItalic().run()}
                disabled={!editor.can().chain().focus().toggleItalic().run()}
                isActive={editor.isActive("italic")}
                title={t("toolbar.italic")}
              >
                <Italic className="h-4 w-4" />
              </ToolbarButton>

              <ToolbarButton
                onClick={() => editor.chain().focus().toggleStrike().run()}
                disabled={!editor.can().chain().focus().toggleStrike().run()}
                isActive={editor.isActive("strike")}
                title={t("toolbar.strikethrough")}
              >
                <Strikethrough className="h-4 w-4" />
              </ToolbarButton>

              <ToolbarButton
                onClick={() => editor.chain().focus().toggleCode().run()}
                disabled={!editor.can().chain().focus().toggleCode().run()}
                isActive={editor.isActive("code")}
                title={t("toolbar.code")}
              >
                <Code className="h-4 w-4" />
              </ToolbarButton>

              <Separator orientation="vertical" className="h-6" />

              {/* Headings */}
              <ToolbarButton
                onClick={() => editor.chain().focus().setParagraph().run()}
                isActive={editor.isActive("paragraph")}
                title={t("toolbar.paragraph")}
              >
                <Type className="h-4 w-4" />
              </ToolbarButton>

              <ToolbarButton
                onClick={() =>
                  editor.chain().focus().toggleHeading({ level: 1 }).run()
                }
                isActive={editor.isActive("heading", { level: 1 })}
                title={t("toolbar.heading1")}
              >
                <Heading1 className="h-4 w-4" />
              </ToolbarButton>

              <ToolbarButton
                onClick={() =>
                  editor.chain().focus().toggleHeading({ level: 2 }).run()
                }
                isActive={editor.isActive("heading", { level: 2 })}
                title={t("toolbar.heading2")}
              >
                <Heading2 className="h-4 w-4" />
              </ToolbarButton>

              <ToolbarButton
                onClick={() =>
                  editor.chain().focus().toggleHeading({ level: 3 }).run()
                }
                isActive={editor.isActive("heading", { level: 3 })}
                title={t("toolbar.heading3")}
              >
                <Heading3 className="h-4 w-4" />
              </ToolbarButton>

              <Separator orientation="vertical" className="h-6" />

              {/* Lists */}
              <ToolbarButton
                onClick={() => editor.chain().focus().toggleBulletList().run()}
                isActive={editor.isActive("bulletList")}
                title={t("toolbar.bullet_list")}
              >
                <List className="h-4 w-4" />
              </ToolbarButton>

              <ToolbarButton
                onClick={() => editor.chain().focus().toggleOrderedList().run()}
                isActive={editor.isActive("orderedList")}
                title={t("toolbar.ordered_list")}
              >
                <ListOrdered className="h-4 w-4" />
              </ToolbarButton>

              <ToolbarButton
                onClick={() => editor.chain().focus().toggleBlockquote().run()}
                isActive={editor.isActive("blockquote")}
                title={t("toolbar.blockquote")}
              >
                <Quote className="h-4 w-4" />
              </ToolbarButton>

              <Separator orientation="vertical" className="h-6" />

              {/* History */}
              <ToolbarButton
                onClick={() => editor.chain().focus().undo().run()}
                disabled={!editor.can().chain().focus().undo().run()}
                title={t("toolbar.undo")}
              >
                <Undo className="h-4 w-4" />
              </ToolbarButton>

              <ToolbarButton
                onClick={() => editor.chain().focus().redo().run()}
                disabled={!editor.can().chain().focus().redo().run()}
                title={t("toolbar.redo")}
              >
                <Redo className="h-4 w-4" />
              </ToolbarButton>

              <Separator orientation="vertical" className="h-6" />

              {/* Advanced */}
              <ToolbarButton
                onClick={() => editor.chain().focus().setHorizontalRule().run()}
                title={t("toolbar.horizontal_rule")}
              >
                <Minus className="h-4 w-4" />
              </ToolbarButton>
            </div>
          </div>
        )}

        <div className="relative">
          <EditorContent editor={editor} />

          {editor && (
            <BubbleMenu
              editor={editor}
              tippyOptions={{ duration: 100 }}
              className="flex gap-1 rounded-lg border border-gray-200 bg-white p-1 shadow-md"
            >
              <Button
                size="sm"
                variant={editor.isActive("bold") ? "default" : "ghost"}
                onMouseDown={(e) => e.preventDefault()}
                onClick={() => editor.chain().focus().toggleBold().run()}
                className="h-8 w-8 p-0"
                title={t("toolbar.bold")}
              >
                <Bold className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant={editor.isActive("italic") ? "default" : "ghost"}
                onMouseDown={(e) => e.preventDefault()}
                onClick={() => editor.chain().focus().toggleItalic().run()}
                className="h-8 w-8 p-0"
                title={t("toolbar.italic")}
              >
                <Italic className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant={editor.isActive("strike") ? "default" : "ghost"}
                onMouseDown={(e) => e.preventDefault()}
                onClick={() => editor.chain().focus().toggleStrike().run()}
                className="h-8 w-8 p-0"
                title={t("toolbar.strikethrough")}
              >
                <Strikethrough className="h-4 w-4" />
              </Button>
            </BubbleMenu>
          )}
        </div>
      </div>
    );
  },
);

TiptapEditor.displayName = "TiptapEditor";

export { TiptapEditor };
