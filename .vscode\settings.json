{"editor.codeActionsOnSave": {"source.organizeImports.biome": "explicit", "source.fixAll.biome": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "i18n-ally.localesPaths": ["messages", "srcparaglide/messages", "src/i18n", "src/paraglide/messages"], "i18n-ally.sourceLanguage": "zh"}