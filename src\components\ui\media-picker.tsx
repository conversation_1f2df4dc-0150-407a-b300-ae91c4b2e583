"use client";

import { api } from "@convex/api";
import { useQuery } from "convex/react";
import {
  Check,
  FileText,
  Image,
  Music,
  Search,
  Video,
  RotateCw,
  RotateCcw,
  FlipHorizontal,
  FlipVertical,
  Edit3,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState, useRef, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Slider } from "@/components/ui/slider";

type FileCategory = "all" | "image" | "video" | "document" | "audio" | "other";

interface MediaFile {
  _id: string;
  originalName: string;
  contentType: string;
  storageId: string;
  category: "image" | "video" | "document" | "audio" | "other";
  size: number;
  url: string | null;
  _creationTime: number;
}

interface ImageTransform {
  rotation: number;
  scaleX: number;
  scaleY: number;
  zoom: number;
  brightness: number;
  contrast: number;
  saturation: number;
}

interface MediaPickerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelect: (file: MediaFile) => void;
  allowedTypes?: FileCategory[];
  multiple?: boolean;
}

export function MediaPicker({
  open,
  onOpenChange,
  onSelect,
  allowedTypes = ["all"],
  multiple = false,
}: MediaPickerProps) {
  const t = useTranslations("media");
  const [selectedCategory, setSelectedCategory] = useState<FileCategory>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFiles, setSelectedFiles] = useState<MediaFile[]>([]);
  const [selectedImage, setSelectedImage] = useState<MediaFile | null>(null);
  const [imageTransform, setImageTransform] = useState<ImageTransform>({
    rotation: 0,
    scaleX: 1,
    scaleY: 1,
    zoom: 1,
    brightness: 100,
    contrast: 100,
    saturation: 100,
  });

  const imageRef = useRef<HTMLImageElement>(null);

  // Reset transform when image changes
  useEffect(() => {
    if (selectedImage) {
      setImageTransform({
        rotation: 0,
        scaleX: 1,
        scaleY: 1,
        zoom: 1,
        brightness: 100,
        contrast: 100,
        saturation: 100,
      });
    }
  }, [selectedImage]);

  // Filter categories based on allowed types
  const availableCategories = allowedTypes.includes("all")
    ? (["all", "image", "video", "document", "audio"] as FileCategory[])
    : ["all", ...allowedTypes];

  const mediaFiles = useQuery(api.media.getMediaFiles, {
    category: selectedCategory,
    searchQuery: searchQuery || undefined,
    limit: 50,
  });

  // Filter files based on allowed types
  const filteredFiles = mediaFiles?.filter((file) => {
    if (allowedTypes.includes("all")) return true;
    return allowedTypes.includes(file.category);
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  };

  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith("image/")) return <Image className="h-4 w-4" />;
    if (contentType.startsWith("video/")) return <Video className="h-4 w-4" />;
    if (contentType.startsWith("audio/")) return <Music className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  const getCategoryBadgeColor = (category: string) => {
    switch (category) {
      case "image":
        return "bg-green-100 text-green-800";
      case "video":
        return "bg-blue-100 text-blue-800";
      case "document":
        return "bg-orange-100 text-orange-800";
      case "audio":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleFileClick = (file: MediaFile) => {
    if (file.category === "image") {
      setSelectedImage(file);
      return;
    }

    if (!multiple) {
      onSelect(file);
      onOpenChange(false);
      return;
    }

    const isSelected = selectedFiles.some((f) => f._id === file._id);
    if (isSelected) {
      setSelectedFiles(selectedFiles.filter((f) => f._id !== file._id));
    } else {
      setSelectedFiles([...selectedFiles, file]);
    }
  };

  const handleSelectMultiple = () => {
    selectedFiles.forEach((file) => onSelect(file));
    onOpenChange(false);
    setSelectedFiles([]);
  };

  const handleClose = () => {
    onOpenChange(false);
    setSelectedFiles([]);
    setSelectedImage(null);
  };

  const handleUseImage = () => {
    if (selectedImage) {
      onSelect(selectedImage);
      onOpenChange(false);
      setSelectedImage(null);
    }
  };

  const handleBackToGrid = () => {
    setSelectedImage(null);
  };

  // Image transformation functions
  const rotateImage = (degrees: number) => {
    setImageTransform((prev) => ({
      ...prev,
      rotation: (prev.rotation + degrees) % 360,
    }));
  };

  const flipImage = (axis: "horizontal" | "vertical") => {
    setImageTransform((prev) => ({
      ...prev,
      scaleX: axis === "horizontal" ? prev.scaleX * -1 : prev.scaleX,
      scaleY: axis === "vertical" ? prev.scaleY * -1 : prev.scaleY,
    }));
  };

  const resetTransform = () => {
    setImageTransform({
      rotation: 0,
      scaleX: 1,
      scaleY: 1,
      zoom: 1,
      brightness: 100,
      contrast: 100,
      saturation: 100,
    });
  };

  const getImageStyle = () => {
    return {
      transform: `rotate(${imageTransform.rotation}deg) scale(${imageTransform.scaleX * imageTransform.zoom}, ${imageTransform.scaleY * imageTransform.zoom})`,
      filter: `brightness(${imageTransform.brightness}%) contrast(${imageTransform.contrast}%) saturate(${imageTransform.saturation}%)`,
      transition: "transform 0.2s ease, filter 0.2s ease",
    };
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="flex h-[80vh] max-w-6xl flex-col">
        <DialogHeader>
          <DialogTitle>
            {selectedImage
              ? `编辑图片: ${selectedImage.originalName}`
              : t("picker.title")}
          </DialogTitle>
        </DialogHeader>

        {selectedImage ? (
          /* Image Editor View */
          <div className="flex flex-1 gap-4">
            {/* Image Preview */}
            <div className="flex flex-1 flex-col">
              <div className="flex flex-1 items-center justify-center overflow-hidden rounded-lg bg-gray-50">
                <div className="relative max-h-full max-w-full">
                  <img
                    ref={imageRef}
                    src={selectedImage.url || ""}
                    alt={selectedImage.originalName}
                    style={getImageStyle()}
                    className="max-h-full max-w-full object-contain"
                  />
                </div>
              </div>
            </div>

            {/* Image Toolbar */}
            <div className="w-72 space-y-4 rounded-lg bg-gray-50 p-4">
              <div className="space-y-3">
                <h3 className="font-medium text-sm">基本操作</h3>

                {/* Rotation */}
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => rotateImage(-90)}
                    className="flex-1"
                  >
                    <RotateCcw className="mr-2 h-4 w-4" />
                    逆时针
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => rotateImage(90)}
                    className="flex-1"
                  >
                    <RotateCw className="mr-2 h-4 w-4" />
                    顺时针
                  </Button>
                </div>

                {/* Flip */}
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => flipImage("horizontal")}
                    className="flex-1"
                  >
                    <FlipHorizontal className="mr-2 h-4 w-4" />
                    水平翻转
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => flipImage("vertical")}
                    className="flex-1"
                  >
                    <FlipVertical className="mr-2 h-4 w-4" />
                    垂直翻转
                  </Button>
                </div>

                <Separator />

                {/* Zoom */}
                <div className="space-y-2">
                  <span className="font-medium text-sm">
                    缩放: {Math.round(imageTransform.zoom * 100)}%
                  </span>
                  <Slider
                    value={[imageTransform.zoom]}
                    onValueChange={(value) =>
                      setImageTransform((prev) => ({ ...prev, zoom: value[0] }))
                    }
                    min={0.1}
                    max={3}
                    step={0.1}
                    className="w-full"
                  />
                </div>

                <Separator />

                {/* Brightness */}
                <div className="space-y-2">
                  <span className="font-medium text-sm">
                    亮度: {imageTransform.brightness}%
                  </span>
                  <Slider
                    value={[imageTransform.brightness]}
                    onValueChange={(value) =>
                      setImageTransform((prev) => ({
                        ...prev,
                        brightness: value[0],
                      }))
                    }
                    min={0}
                    max={200}
                    step={1}
                    className="w-full"
                  />
                </div>

                {/* Contrast */}
                <div className="space-y-2">
                  <span className="font-medium text-sm">
                    对比度: {imageTransform.contrast}%
                  </span>
                  <Slider
                    value={[imageTransform.contrast]}
                    onValueChange={(value) =>
                      setImageTransform((prev) => ({
                        ...prev,
                        contrast: value[0],
                      }))
                    }
                    min={0}
                    max={200}
                    step={1}
                    className="w-full"
                  />
                </div>

                {/* Saturation */}
                <div className="space-y-2">
                  <span className="font-medium text-sm">
                    饱和度: {imageTransform.saturation}%
                  </span>
                  <Slider
                    value={[imageTransform.saturation]}
                    onValueChange={(value) =>
                      setImageTransform((prev) => ({
                        ...prev,
                        saturation: value[0],
                      }))
                    }
                    min={0}
                    max={200}
                    step={1}
                    className="w-full"
                  />
                </div>

                <Separator />

                {/* Reset */}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={resetTransform}
                  className="w-full"
                >
                  重置所有修改
                </Button>
              </div>

              {/* File Info */}
              <div className="space-y-2 border-t pt-4">
                <h4 className="font-medium text-sm">文件信息</h4>
                <div className="space-y-1 text-gray-600 text-xs">
                  <p>文件名: {selectedImage.originalName}</p>
                  <p>大小: {formatFileSize(selectedImage.size)}</p>
                  <p>类型: {selectedImage.contentType}</p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* File Grid View */
          <>
            {/* Search and Filters */}
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="flex flex-wrap gap-2">
                {availableCategories.map((category) => (
                  <Button
                    key={category}
                    variant={
                      selectedCategory === category ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() =>
                      setSelectedCategory(category as FileCategory)
                    }
                  >
                    {category === "image" && <Image className="mr-2 h-4 w-4" />}
                    {category === "video" && <Video className="mr-2 h-4 w-4" />}
                    {category === "document" && (
                      <FileText className="mr-2 h-4 w-4" />
                    )}
                    {category === "audio" && <Music className="mr-2 h-4 w-4" />}
                    {t(
                      `filters.${category === "document" ? "documents" : category === "all" ? "all" : `${category}s`}`,
                    )}
                  </Button>
                ))}
              </div>

              <div className="relative w-full md:w-64">
                <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder={t("picker.search")}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Selected Files Indicator */}
            {multiple && selectedFiles.length > 0 && (
              <div className="flex items-center gap-2 rounded bg-blue-50 p-2 text-blue-600 text-sm">
                <Check className="h-4 w-4" />
                {t("picker.selected")}: {selectedFiles.length}
              </div>
            )}

            {/* File Grid */}
            <ScrollArea className="flex-1">
              {filteredFiles && filteredFiles.length > 0 ? (
                <div className="grid grid-cols-1 gap-4 p-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                  {filteredFiles.map((file) => {
                    const isSelected = selectedFiles.some(
                      (f) => f._id === file._id,
                    );
                    return (
                      <button
                        key={file._id}
                        type="button"
                        className={`relative w-full cursor-pointer rounded-lg border-2 p-3 text-left transition-all hover:border-primary/50 ${
                          isSelected
                            ? "border-primary bg-primary/5"
                            : "border-gray-200"
                        }`}
                        onClick={() => handleFileClick(file)}
                      >
                        {isSelected && (
                          <div className="absolute top-2 right-2 z-10">
                            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-white">
                              <Check className="h-4 w-4" />
                            </div>
                          </div>
                        )}

                        {/* Edit icon for images */}
                        {file.category === "image" && (
                          <div className="absolute top-2 left-2 z-10">
                            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-black/50 text-white">
                              <Edit3 className="h-3 w-3" />
                            </div>
                          </div>
                        )}

                        <div className="space-y-3">
                          {file.category === "image" && file.url ? (
                            <div className="aspect-video overflow-hidden rounded bg-gray-100">
                              <img
                                src={file.url}
                                alt={file.originalName}
                                className="h-full w-full object-cover"
                              />
                            </div>
                          ) : (
                            <div className="flex aspect-video items-center justify-center rounded bg-gray-100">
                              {getFileIcon(file.contentType)}
                            </div>
                          )}

                          <div className="space-y-2">
                            <h3 className="truncate font-medium text-sm">
                              {file.originalName}
                            </h3>

                            <div className="flex items-center justify-between">
                              <Badge
                                className={getCategoryBadgeColor(file.category)}
                              >
                                {t(
                                  `filters.${file.category === "document" ? "documents" : `${file.category}s`}`,
                                )}
                              </Badge>
                              <span className="text-gray-500 text-xs">
                                {formatFileSize(file.size)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>
              ) : (
                <div className="flex h-64 flex-col items-center justify-center text-gray-500">
                  <FileText className="mb-4 h-12 w-12" />
                  <p>{t("messages.no_files")}</p>
                </div>
              )}
            </ScrollArea>
          </>
        )}

        <DialogFooter>
          {selectedImage ? (
            <>
              <Button variant="outline" onClick={handleBackToGrid}>
                返回列表
              </Button>
              <Button onClick={handleUseImage}>使用此图片</Button>
            </>
          ) : (
            <>
              <Button variant="outline" onClick={handleClose}>
                {t("picker.cancel")}
              </Button>
              {multiple && selectedFiles.length > 0 && (
                <Button onClick={handleSelectMultiple}>
                  {t("picker.select")} ({selectedFiles.length})
                </Button>
              )}
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Hook for easier usage
export function useMediaPicker() {
  const [isOpen, setIsOpen] = useState(false);

  return {
    isOpen,
    openPicker: () => setIsOpen(true),
    closePicker: () => setIsOpen(false),
    MediaPickerComponent: MediaPicker,
  };
}
