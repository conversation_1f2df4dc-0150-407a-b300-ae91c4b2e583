"use client";

import { api } from "@convex/api";
import { useQuery } from "convex/react";
import {
  ArrowLeft,
  Check,
  Download,
  FileText,
  Filter,
  Grid3X3,
  Image as ImageIcon,
  List,
  Music,
  Search,
  Upload,
  Video,
  X,
} from "lucide-react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

type FileCategory = "all" | "image" | "video" | "document" | "audio" | "other";
type ViewMode = "grid" | "list";

interface MediaFile {
  _id: string;
  originalName: string;
  contentType: string;
  storageId: string;
  category: "image" | "video" | "document" | "audio" | "other";
  size: number;
  url: string | null;
  _creationTime: number;
}

interface MediaPickerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelect: (file: MediaFile) => void;
  allowedTypes?: FileCategory[];
  multiple?: boolean;
}

export function MediaPicker({
  open,
  onOpenChange,
  onSelect,
  allowedTypes = ["all"],
  multiple = false,
}: MediaPickerProps) {
  const t = useTranslations("media");
  const [selectedCategory, setSelectedCategory] = useState<FileCategory>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFiles, setSelectedFiles] = useState<MediaFile[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [selectedImage, setSelectedImage] = useState<MediaFile | null>(null);

  const mediaFiles = useQuery(api.media.getMediaFiles, {
    category: selectedCategory,
    searchQuery: searchQuery || undefined,
    limit: 50,
  });

  // Filter files based on allowed types
  const filteredFiles = mediaFiles?.filter((file) => {
    if (allowedTypes.includes("all")) return true;
    return allowedTypes.includes(file.category);
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / k ** i).toFixed(1))} ${sizes[i]}`;
  };

  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith("image/"))
      return <ImageIcon className="h-4 w-4" />;
    if (contentType.startsWith("video/")) return <Video className="h-4 w-4" />;
    if (contentType.startsWith("audio/")) return <Music className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  const handleFileClick = (file: MediaFile) => {
    if (file.category === "image") {
      setSelectedImage(file);
      return;
    }

    if (!multiple) {
      onSelect(file);
      onOpenChange(false);
      return;
    }

    const isSelected = selectedFiles.some((f) => f._id === file._id);
    if (isSelected) {
      setSelectedFiles(selectedFiles.filter((f) => f._id !== file._id));
    } else {
      setSelectedFiles([...selectedFiles, file]);
    }
  };

  const handleSelectMultiple = () => {
    selectedFiles.forEach((file) => onSelect(file));
    onOpenChange(false);
    setSelectedFiles([]);
  };

  const handleClose = () => {
    onOpenChange(false);
    setSelectedFiles([]);
    setSelectedImage(null);
  };

  const handleUseImage = () => {
    if (selectedImage) {
      onSelect(selectedImage);
      onOpenChange(false);
      setSelectedImage(null);
    }
  };

  const handleBackToGrid = () => {
    setSelectedImage(null);
  };

  // Category filters with Apple-style design
  const categoryFilters = [
    { key: "all", label: "全部", icon: Filter },
    { key: "image", label: "图片", icon: ImageIcon },
    { key: "video", label: "视频", icon: Video },
    { key: "document", label: "文档", icon: FileText },
    { key: "audio", label: "音频", icon: Music },
  ].filter(
    (filter) =>
      allowedTypes.includes("all") ||
      allowedTypes.includes(filter.key as FileCategory),
  );

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="h-[85vh] max-w-7xl overflow-hidden p-0">
        {selectedImage ? (
          // Image Preview Mode - Apple Photos style
          <div className="flex h-full flex-col bg-black">
            {/* Header */}
            <div className="flex items-center justify-between bg-black/90 p-4 text-white backdrop-blur-sm">
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToGrid}
                  className="text-white hover:bg-white/10"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  返回
                </Button>
                <div>
                  <h3 className="font-medium">{selectedImage.originalName}</h3>
                  <p className="text-sm text-white/70">
                    {formatFileSize(selectedImage.size)}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-white/10"
                >
                  <Download className="h-4 w-4" />
                </Button>
                <Button
                  onClick={handleUseImage}
                  className="bg-blue-600 text-white hover:bg-blue-700"
                >
                  选择此图片
                </Button>
              </div>
            </div>

            {/* Image Display */}
            <div className="flex flex-1 items-center justify-center p-8">
              <div className="relative max-h-full max-w-full">
                {selectedImage.url && (
                  <Image
                    src={selectedImage.url}
                    alt={selectedImage.originalName}
                    width={800}
                    height={600}
                    className="max-h-full max-w-full rounded-lg object-contain"
                    style={{ maxHeight: "calc(85vh - 120px)" }}
                  />
                )}
              </div>
            </div>
          </div>
        ) : (
          // Grid/List Mode - Apple Finder style
          <div className="flex h-full flex-col">
            {/* Header */}
            <DialogHeader className="border-b bg-gray-50/50 p-6 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <DialogTitle className="font-semibold text-xl">
                  选择媒体文件
                </DialogTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClose}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </DialogHeader>

            {/* Toolbar */}
            <div className="border-b bg-white p-4">
              <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
                {/* Category Filters */}
                <div className="flex flex-wrap gap-2">
                  {categoryFilters.map((filter) => {
                    const Icon = filter.icon;
                    return (
                      <Button
                        key={filter.key}
                        variant={
                          selectedCategory === filter.key
                            ? "default"
                            : "outline"
                        }
                        size="sm"
                        onClick={() =>
                          setSelectedCategory(filter.key as FileCategory)
                        }
                        className={cn(
                          "transition-all duration-200",
                          selectedCategory === filter.key
                            ? "bg-blue-600 text-white shadow-sm"
                            : "hover:bg-gray-50",
                        )}
                      >
                        <Icon className="mr-2 h-4 w-4" />
                        {filter.label}
                      </Button>
                    );
                  })}
                </div>

                {/* Search and View Controls */}
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="搜索文件..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-64 pl-10"
                    />
                  </div>
                  <div className="flex rounded-lg border">
                    <Button
                      variant={viewMode === "grid" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("grid")}
                      className="rounded-r-none"
                    >
                      <Grid3X3 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === "list" ? "default" : "ghost"}
                      size="sm"
                      onClick={() => setViewMode("list")}
                      className="rounded-l-none"
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Selected Files Indicator */}
              {multiple && selectedFiles.length > 0 && (
                <div className="mt-4 flex items-center justify-between rounded-lg bg-blue-50 p-3">
                  <div className="flex items-center gap-2 text-blue-700">
                    <Check className="h-4 w-4" />
                    <span className="font-medium">
                      已选择 {selectedFiles.length} 个文件
                    </span>
                  </div>
                  <Button onClick={handleSelectMultiple} size="sm">
                    使用选中的文件
                  </Button>
                </div>
              )}
            </div>

            {/* Content Area */}
            <ScrollArea className="flex-1 p-6">
              {filteredFiles && filteredFiles.length > 0 ? (
                viewMode === "grid" ? (
                  // Grid View - Apple Photos style
                  <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
                    {filteredFiles.map((file) => {
                      const isSelected = selectedFiles.some(
                        (f) => f._id === file._id,
                      );
                      return (
                        <button
                          key={file._id}
                          type="button"
                          className={cn(
                            "group relative w-full overflow-hidden rounded-xl border-2 transition-all duration-200 hover:shadow-lg",
                            isSelected
                              ? "border-blue-500 ring-2 ring-blue-200"
                              : "border-gray-200 hover:border-gray-300",
                          )}
                          onClick={() => handleFileClick(file)}
                        >
                          {/* Selection Indicator */}
                          {isSelected && (
                            <div className="absolute top-2 right-2 z-10">
                              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-600 text-white shadow-sm">
                                <Check className="h-3 w-3" />
                              </div>
                            </div>
                          )}

                          {/* Preview */}
                          <div className="aspect-square bg-gray-50">
                            {file.category === "image" && file.url ? (
                              <Image
                                src={file.url}
                                alt={file.originalName}
                                width={200}
                                height={200}
                                className="h-full w-full object-cover transition-transform duration-200 group-hover:scale-105"
                              />
                            ) : (
                              <div className="flex h-full items-center justify-center">
                                <div className="text-center">
                                  {getFileIcon(file.contentType)}
                                  <p className="mt-2 text-gray-500 text-xs">
                                    {file.category.toUpperCase()}
                                  </p>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* File Info */}
                          <div className="p-3">
                            <h3 className="truncate font-medium text-gray-900 text-sm">
                              {file.originalName}
                            </h3>
                            <p className="text-gray-500 text-xs">
                              {formatFileSize(file.size)}
                            </p>
                          </div>

                          {/* Hover Overlay */}
                          <div className="absolute inset-0 bg-black/0 transition-colors duration-200 group-hover:bg-black/5" />
                        </button>
                      );
                    })}
                  </div>
                ) : (
                  // List View - Apple Finder style
                  <div className="space-y-1">
                    {filteredFiles.map((file) => {
                      const isSelected = selectedFiles.some(
                        (f) => f._id === file._id,
                      );
                      return (
                        <button
                          key={file._id}
                          type="button"
                          className={cn(
                            "flex w-full items-center gap-4 rounded-lg p-3 text-left transition-all duration-200 hover:bg-gray-50",
                            isSelected && "bg-blue-50 ring-1 ring-blue-200",
                          )}
                          onClick={() => handleFileClick(file)}
                        >
                          {/* File Icon/Preview */}
                          <div className="flex-shrink-0">
                            {file.category === "image" && file.url ? (
                              <div className="h-12 w-12 overflow-hidden rounded-lg">
                                <Image
                                  src={file.url}
                                  alt={file.originalName}
                                  width={48}
                                  height={48}
                                  className="h-full w-full object-cover"
                                />
                              </div>
                            ) : (
                              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100">
                                {getFileIcon(file.contentType)}
                              </div>
                            )}
                          </div>

                          {/* File Details */}
                          <div className="min-w-0 flex-1">
                            <h3 className="truncate font-medium text-gray-900">
                              {file.originalName}
                            </h3>
                            <div className="flex items-center gap-2 text-gray-500 text-sm">
                              <Badge variant="secondary" className="text-xs">
                                {file.category}
                              </Badge>
                              <span>{formatFileSize(file.size)}</span>
                              <span>•</span>
                              <span>
                                {new Date(
                                  file._creationTime,
                                ).toLocaleDateString()}
                              </span>
                            </div>
                          </div>

                          {/* Selection Indicator */}
                          {isSelected && (
                            <div className="flex-shrink-0">
                              <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-white">
                                <Check className="h-3 w-3" />
                              </div>
                            </div>
                          )}
                        </button>
                      );
                    })}
                  </div>
                )
              ) : (
                // Empty State
                <div className="flex h-64 flex-col items-center justify-center text-gray-500">
                  <Upload className="mb-4 h-12 w-12 text-gray-300" />
                  <h3 className="mb-2 font-medium">没有找到文件</h3>
                  <p className="text-sm">尝试调整搜索条件或上传新文件</p>
                </div>
              )}
            </ScrollArea>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

// Hook for easier usage
export function useMediaPicker() {
  const [isOpen, setIsOpen] = useState(false);

  return {
    isOpen,
    openPicker: () => setIsOpen(true),
    closePicker: () => setIsOpen(false),
    MediaPickerComponent: MediaPicker,
  };
}
