"use client";

import { api } from "@convex/api";
import { useQuery } from "convex/react";
import {
  ArrowLeft,
  Check,
  Download,
  FileText,
  Filter,
  Grid3X3,
  Image as ImageIcon,
  List,
  Music,
  Search,
  Upload,
  Video,
  X,
} from "lucide-react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

type FileCategory = "all" | "image" | "video" | "document" | "audio" | "other";
type ViewMode = "grid" | "list";

interface MediaFile {
  _id: string;
  originalName: string;
  contentType: string;
  storageId: string;
  category: "image" | "video" | "document" | "audio" | "other";
  size: number;
  url: string | null;
  _creationTime: number;
}

interface MediaPickerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelect: (file: MediaFile) => void;
  allowedTypes?: FileCategory[];
  multiple?: boolean;
}

export function MediaPicker({
  open,
  onOpenChange,
  onSelect,
  allowedTypes = ["all"],
  multiple = false,
}: MediaPickerProps) {
  const t = useTranslations("media");
  const [selectedCategory, setSelectedCategory] = useState<FileCategory>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFiles, setSelectedFiles] = useState<MediaFile[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [selectedImage, setSelectedImage] = useState<MediaFile | null>(null);

  const mediaFiles = useQuery(api.media.getMediaFiles, {
    category: selectedCategory,
    searchQuery: searchQuery || undefined,
    limit: 50,
  });

  // Filter files based on allowed types
  const filteredFiles = mediaFiles?.filter((file) => {
    if (allowedTypes.includes("all")) return true;
    return allowedTypes.includes(file.category);
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / k ** i).toFixed(1))} ${sizes[i]}`;
  };

  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith("image/"))
      return <ImageIcon className="h-4 w-4" />;
    if (contentType.startsWith("video/")) return <Video className="h-4 w-4" />;
    if (contentType.startsWith("audio/")) return <Music className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  const handleFileClick = (file: MediaFile) => {
    if (file.category === "image") {
      setSelectedImage(file);
      return;
    }

    if (!multiple) {
      onSelect(file);
      onOpenChange(false);
      return;
    }

    const isSelected = selectedFiles.some((f) => f._id === file._id);
    if (isSelected) {
      setSelectedFiles(selectedFiles.filter((f) => f._id !== file._id));
    } else {
      setSelectedFiles([...selectedFiles, file]);
    }
  };

  const handleSelectMultiple = () => {
    selectedFiles.forEach((file) => onSelect(file));
    onOpenChange(false);
    setSelectedFiles([]);
  };

  const handleClose = () => {
    onOpenChange(false);
    setSelectedFiles([]);
    setSelectedImage(null);
  };

  const handleUseImage = () => {
    if (selectedImage) {
      onSelect(selectedImage);
      onOpenChange(false);
      setSelectedImage(null);
    }
  };

  const handleBackToGrid = () => {
    setSelectedImage(null);
  };

  // Category filters - responsive design
  const categoryFilters = [
    { key: "all", label: "全部", icon: Filter },
    { key: "image", label: "图片", icon: ImageIcon },
    { key: "video", label: "视频", icon: Video },
    { key: "document", label: "文档", icon: FileText },
    { key: "audio", label: "音频", icon: Music },
  ].filter(
    (filter) =>
      allowedTypes.includes("all") ||
      allowedTypes.includes(filter.key as FileCategory),
  );

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="h-[90vh] w-[95vw] max-w-6xl overflow-hidden p-0 sm:h-[85vh] sm:w-[90vw]">
        {selectedImage ? (
          // Image Preview Mode - Full responsive
          <div className="flex h-full flex-col bg-black">
            {/* Header - Mobile optimized */}
            <div className="flex items-center justify-between bg-black/90 p-3 text-white backdrop-blur-sm sm:p-4">
              <div className="flex min-w-0 flex-1 items-center gap-2 sm:gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToGrid}
                  className="flex-shrink-0 text-white hover:bg-white/10"
                >
                  <ArrowLeft className="h-4 w-4 sm:mr-2" />
                  <span className="hidden sm:inline">返回</span>
                </Button>
                <div className="min-w-0 flex-1">
                  <h3 className="truncate font-medium text-sm sm:text-base">
                    {selectedImage.originalName}
                  </h3>
                  <p className="text-white/70 text-xs sm:text-sm">
                    {formatFileSize(selectedImage.size)}
                  </p>
                </div>
              </div>
              <div className="flex flex-shrink-0 items-center gap-1 sm:gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-white hover:bg-white/10 sm:h-auto sm:w-auto sm:p-2"
                >
                  <Download className="h-4 w-4" />
                </Button>
                <Button
                  onClick={handleUseImage}
                  size="sm"
                  className="bg-blue-600 text-white text-xs hover:bg-blue-700 sm:text-sm"
                >
                  选择
                </Button>
              </div>
            </div>

            {/* Image Display - Responsive */}
            <div className="flex flex-1 items-center justify-center p-4 sm:p-8">
              <div className="relative max-h-full max-w-full">
                {selectedImage.url && (
                  <Image
                    src={selectedImage.url}
                    alt={selectedImage.originalName}
                    width={800}
                    height={600}
                    className="max-h-full max-w-full rounded-lg object-contain"
                    style={{ maxHeight: "calc(90vh - 100px)" }}
                  />
                )}
              </div>
            </div>
          </div>
        ) : (
          // Grid/List Mode - Completely responsive
          <div className="flex h-full flex-col">
            {/* Header - Mobile first */}
            <DialogHeader className="border-b bg-gray-50/50 p-4 backdrop-blur-sm sm:p-6">
              <div className="flex items-center justify-between">
                <DialogTitle className="font-semibold text-lg sm:text-xl">
                  选择媒体文件
                </DialogTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClose}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </DialogHeader>

            {/* Toolbar - Responsive layout */}
            <div className="border-b bg-white p-3 sm:p-4">
              {/* Category Filters - Horizontal scroll on mobile */}
              <div className="flex gap-2 overflow-x-auto pb-2 sm:flex-wrap sm:pb-0">
                {categoryFilters.map((filter) => {
                  const Icon = filter.icon;
                  return (
                    <Button
                      key={filter.key}
                      variant={
                        selectedCategory === filter.key ? "default" : "outline"
                      }
                      size="sm"
                      onClick={() =>
                        setSelectedCategory(filter.key as FileCategory)
                      }
                      className={cn(
                        "flex-shrink-0 text-xs transition-all duration-200 sm:text-sm",
                        selectedCategory === filter.key
                          ? "bg-blue-600 text-white shadow-sm"
                          : "hover:bg-gray-50",
                      )}
                    >
                      <Icon className="mr-1 h-3 w-3 sm:mr-2 sm:h-4 sm:w-4" />
                      {filter.label}
                    </Button>
                  );
                })}
              </div>

              {/* Search and View Controls - Stacked on mobile */}
              <div className="mt-3 flex flex-col gap-3 sm:mt-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="relative max-w-md flex-1">
                  <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="搜索文件..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 text-sm"
                  />
                </div>
                <div className="flex self-start rounded-lg border sm:self-auto">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-r-none"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Selected Files Indicator - Mobile optimized */}
              {multiple && selectedFiles.length > 0 && (
                <div className="mt-3 flex flex-col gap-2 rounded-lg bg-blue-50 p-3 sm:flex-row sm:items-center sm:justify-between">
                  <div className="flex items-center gap-2 text-blue-700">
                    <Check className="h-4 w-4" />
                    <span className="font-medium text-sm">
                      已选择 {selectedFiles.length} 个文件
                    </span>
                  </div>
                  <Button
                    onClick={handleSelectMultiple}
                    size="sm"
                    className="self-start sm:self-auto"
                  >
                    使用选中的文件
                  </Button>
                </div>
              )}
            </div>

            {/* Content Area - Responsive Grid/List */}
            <ScrollArea className="flex-1 p-3 sm:p-6">
              {!filteredFiles ? (
                // Loading state
                <div className="flex h-64 items-center justify-center">
                  <div className="text-center">
                    <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-blue-600 border-b-2"></div>
                    <p className="text-gray-500">加载中...</p>
                  </div>
                </div>
              ) : filteredFiles.length === 0 ? (
                // Empty state
                <div className="flex h-64 flex-col items-center justify-center text-center">
                  <Upload className="mb-4 h-12 w-12 text-gray-300" />
                  <h3 className="mb-2 font-medium text-gray-900 text-lg">
                    暂无文件
                  </h3>
                  <p className="mb-4 max-w-sm text-gray-500">
                    {searchQuery ? "没有找到匹配的文件" : "还没有上传任何文件"}
                  </p>
                  {!searchQuery && (
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      <Upload className="mr-2 h-4 w-4" />
                      上传文件
                    </Button>
                  )}
                </div>
              ) : viewMode === "grid" ? (
                // Grid View - Responsive grid
                <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
                  {filteredFiles.map((file) => {
                    const isSelected = selectedFiles.some(
                      (f) => f._id === file._id,
                    );
                    const isImage = file.category === "image";

                    return (
                      <button
                        key={file._id}
                        type="button"
                        onClick={() => handleFileClick(file)}
                        className={cn(
                          "group relative w-full rounded-lg border-2 transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                          isSelected
                            ? "border-blue-500 bg-blue-50 shadow-sm"
                            : "border-gray-200 hover:border-gray-300",
                          "aspect-square overflow-hidden",
                        )}
                        aria-label={`选择文件 ${file.originalName}`}
                      >
                        {/* Selection indicator */}
                        {multiple && (
                          <div
                            className={cn(
                              "absolute top-2 right-2 z-10 h-5 w-5 rounded-full border-2 transition-all duration-200",
                              isSelected
                                ? "border-blue-600 bg-blue-600"
                                : "border-gray-300 bg-white group-hover:border-gray-400",
                            )}
                          >
                            {isSelected && (
                              <Check className="absolute top-0.5 left-0.5 h-3 w-3 text-white" />
                            )}
                          </div>
                        )}

                        {/* File preview */}
                        <div className="flex h-full w-full flex-col">
                          {isImage && file.url ? (
                            <div className="relative flex-1 bg-gray-100">
                              <Image
                                src={file.url}
                                alt={file.originalName}
                                fill
                                className="object-cover"
                                sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, (max-width: 1280px) 20vw, 16vw"
                              />
                            </div>
                          ) : (
                            <div className="flex flex-1 items-center justify-center bg-gray-50">
                              {getFileIcon(file.contentType)}
                            </div>
                          )}

                          {/* File info */}
                          <div className="border-t bg-white p-2">
                            <p className="truncate font-medium text-gray-900 text-xs">
                              {file.originalName}
                            </p>
                            <p className="text-gray-500 text-xs">
                              {formatFileSize(file.size)}
                            </p>
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>
              ) : (
                // List View - Mobile optimized
                <div className="space-y-2">
                  {filteredFiles.map((file) => {
                    const isSelected = selectedFiles.some(
                      (f) => f._id === file._id,
                    );
                    const isImage = file.category === "image";

                    return (
                      <button
                        key={file._id}
                        type="button"
                        onClick={() => handleFileClick(file)}
                        className={cn(
                          "flex w-full items-center gap-3 rounded-lg border p-3 transition-all duration-200 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
                          isSelected
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:border-gray-300",
                        )}
                        aria-label={`选择文件 ${file.originalName}`}
                      >
                        {/* Selection indicator */}
                        {multiple && (
                          <div
                            className={cn(
                              "h-5 w-5 flex-shrink-0 rounded-full border-2 transition-all duration-200",
                              isSelected
                                ? "border-blue-600 bg-blue-600"
                                : "border-gray-300 bg-white",
                            )}
                          >
                            {isSelected && (
                              <Check className="absolute top-0.5 left-0.5 h-3 w-3 text-white" />
                            )}
                          </div>
                        )}

                        {/* File thumbnail */}
                        <div className="h-12 w-12 flex-shrink-0 overflow-hidden rounded-lg bg-gray-100">
                          {isImage && file.url ? (
                            <Image
                              src={file.url}
                              alt={file.originalName}
                              width={48}
                              height={48}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <div className="flex h-full w-full items-center justify-center">
                              {getFileIcon(file.contentType)}
                            </div>
                          )}
                        </div>

                        {/* File info */}
                        <div className="min-w-0 flex-1">
                          <p className="truncate font-medium text-gray-900 text-sm">
                            {file.originalName}
                          </p>
                          <div className="flex items-center gap-2 text-gray-500 text-xs">
                            <span>{formatFileSize(file.size)}</span>
                            <span>•</span>
                            <span>
                              {new Date(
                                file._creationTime,
                              ).toLocaleDateString()}
                            </span>
                          </div>
                        </div>

                        {/* File type badge */}
                        <Badge
                          variant="secondary"
                          className="flex-shrink-0 text-xs"
                        >
                          {file.category}
                        </Badge>
                      </button>
                    );
                  })}
                </div>
              )}
            </ScrollArea>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}

// Hook for easier usage
export function useMediaPicker() {
  const [isOpen, setIsOpen] = useState(false);

  return {
    isOpen,
    openPicker: () => setIsOpen(true),
    closePicker: () => setIsOpen(false),
    MediaPickerComponent: MediaPicker,
  };
}
