import { NextIntlClientProvider } from "next-intl";
import { Toaster } from "sonner";
import { ConvexClientProvider } from "./convex-client-provider";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ConvexClientProvider>
      <NextIntlClientProvider>
        {children}
        <Toaster
          position="bottom-right"
          expand={true}
          richColors={true}
          closeButton={true}
        />
      </NextIntlClientProvider>
    </ConvexClientProvider>
  );
}
