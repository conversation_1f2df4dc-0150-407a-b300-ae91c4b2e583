"use client";

import { api } from "@convex/api";
import { useQuery } from "convex/react";
import { ImageIcon, RotateCcw, Upload, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MediaPicker } from "@/components/ui/media-picker";

interface MediaFile {
  _id: string;
  originalName: string;
  contentType: string;
  storageId: string;
  url: string | null;
}

interface CoverImagePickerProps {
  value?: string; // storageId 或 URL
  onChange: (storageId: string | undefined) => void;
  className?: string;
  extractFromContent?: () => string | null; // 从内容中提取图片的函数，现在返回URL
}

export function CoverImagePicker({
  value,
  onChange,
  className,
  extractFromContent,
}: CoverImagePickerProps) {
  const t = useTranslations("media.cover_picker");
  const [isPickerOpen, setIsPickerOpen] = useState(false);

  // 判断value是URL还是storageId
  const isUrl = value?.includes("http");

  // 查询当前封面图信息
  const coverImageByStorageId = useQuery(
    api.media.getMediaFileByStorageId,
    !isUrl && value ? { storageId: value as any } : "skip",
  );

  const coverImageByUrl = useQuery(
    api.media.getMediaFileByUrl,
    isUrl && value ? { url: value } : "skip",
  );

  const coverImage = isUrl ? coverImageByUrl : coverImageByStorageId;

  // 当通过URL找到文件后，更新为storageId
  useEffect(() => {
    if (isUrl && coverImageByUrl?.storageId) {
      onChange(coverImageByUrl.storageId);
    }
  }, [isUrl, coverImageByUrl, onChange]);

  const handleSelectImage = (file: MediaFile) => {
    onChange(file.storageId);
    setIsPickerOpen(false);
  };

  const handleRemoveImage = () => {
    onChange(undefined);
  };

  const handleExtractFromContent = () => {
    if (extractFromContent) {
      const extractedUrl = extractFromContent();
      if (extractedUrl) {
        // 设置URL，让组件查询对应的文件并转换为storageId
        onChange(extractedUrl);
      }
    }
  };

  return (
    <>
      <div className={className}>
        {coverImage ? (
          // 显示已选择的封面图
          <Card className="overflow-hidden">
            <CardContent className="p-0">
              <div className="group relative">
                <div className="aspect-video overflow-hidden bg-gray-100">
                  <img
                    src={coverImage.url || ""}
                    alt={coverImage.originalName}
                    className="h-full w-full object-cover"
                  />
                </div>

                {/* 悬浮操作按钮 */}
                <div className="absolute inset-0 flex items-center justify-center gap-2 bg-black/50 opacity-0 transition-opacity group-hover:opacity-100">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => setIsPickerOpen(true)}
                    className="bg-white/90 text-gray-900 hover:bg-white"
                  >
                    <ImageIcon className="mr-2 h-4 w-4" />
                    {t("change_image")}
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={handleRemoveImage}
                    className="bg-white/90 text-gray-900 hover:bg-white"
                  >
                    <X className="mr-2 h-4 w-4" />
                    {t("remove_image")}
                  </Button>
                </div>

                {/* 图片信息 */}
                <div className="absolute right-0 bottom-0 left-0 bg-black/70 p-2 text-white text-xs">
                  <p className="truncate">{coverImage.originalName}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          // 显示选择封面图的界面
          <Card className="border-2 border-gray-300 border-dashed transition-colors hover:border-gray-400">
            <CardContent className="p-6">
              <div className="flex flex-col items-center justify-center space-y-4 text-center">
                <div className="rounded-full bg-gray-100 p-4">
                  <ImageIcon className="h-8 w-8 text-gray-400" />
                </div>

                <div>
                  <h3 className="mb-1 font-medium text-gray-900">
                    {t("select_cover")}
                  </h3>
                  <p className="text-gray-500 text-sm">
                    {t("select_cover_description")}
                  </p>
                </div>

                <div className="flex w-full max-w-sm flex-col gap-2 sm:flex-row">
                  <Button
                    onClick={() => setIsPickerOpen(true)}
                    className="flex-1 gap-2"
                    variant="outline"
                  >
                    <Upload className="h-4 w-4" />
                    {t("select_image")}
                  </Button>

                  {extractFromContent && (
                    <Button
                      onClick={handleExtractFromContent}
                      className="flex-1 gap-2"
                      variant="outline"
                    >
                      <RotateCcw className="h-4 w-4" />
                      {t("extract_from_content")}
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <MediaPicker
        open={isPickerOpen}
        onOpenChange={setIsPickerOpen}
        onSelect={handleSelectImage}
        allowedTypes={["image"]}
        multiple={false}
      />
    </>
  );
}
