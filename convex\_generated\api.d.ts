/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as auth from "../auth.js";
import type * as examples from "../examples.js";
import type * as http from "../http.js";
import type * as media from "../media.js";
import type * as news from "../news.js";
import type * as productCategories from "../productCategories.js";
import type * as productStandards from "../productStandards.js";
import type * as products from "../products.js";
import type * as task from "../task.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  auth: typeof auth;
  examples: typeof examples;
  http: typeof http;
  media: typeof media;
  news: typeof news;
  productCategories: typeof productCategories;
  productStandards: typeof productStandards;
  products: typeof products;
  task: typeof task;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
