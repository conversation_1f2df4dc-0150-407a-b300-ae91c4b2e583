"use client";

import { api } from "@convex/api";
import { useAuthActions } from "@convex-dev/auth/react";
import {
  Authenticated,
  AuthLoading,
  Unauthenticated,
  useQuery,
} from "convex/react";

export default function Home() {
  const { signOut } = useAuthActions();
  const currentUser = useQuery(api.users.current);

  return (
    <>
      <AuthLoading>
        <div className="flex min-h-screen items-center justify-center">
          <div>加载中...</div>
        </div>
      </AuthLoading>

      <Unauthenticated>
        <div className="flex min-h-screen items-center justify-center">
          <div>请先登录</div>
        </div>
      </Unauthenticated>

      <Authenticated>
        <div className="flex min-h-screen items-center justify-center">
          <div className="text-center">
            <h1 className="mb-4 font-bold text-2xl">欢迎回来!</h1>
            {currentUser && (
              <div className="mb-4">
                <p className="text-lg">
                  用户名: {currentUser.name || currentUser.email}
                </p>
                <p className="text-gray-600">邮箱: {currentUser.email}</p>
              </div>
            )}
            <button
              type="button"
              onClick={() => signOut()}
              className="rounded bg-red-500 px-4 py-2 text-white hover:bg-red-600"
            >
              登出
            </button>
          </div>
        </div>
      </Authenticated>
    </>
  );
}
