"use client";

import { useRef, useState } from "react";
import { Button } from "./button";
import { TiptapEditor, type TiptapEditorRef } from "./tiptap-editor";

export function TiptapExample() {
  const [content, setContent] = useState(
    "<p>Hello world! 这是一个示例编辑器。</p>",
  );
  const editorRef = useRef<TiptapEditorRef>(null);

  const handleSave = () => {
    if (editorRef.current) {
      const html = editorRef.current.getHTML();
      console.log("保存的内容:", html);
      alert("内容已保存到控制台！");
    }
  };

  const handleClear = () => {
    if (editorRef.current) {
      editorRef.current.clear();
      setContent("");
    }
  };

  const handleLoadSample = () => {
    const sampleContent = `
      <h1>欢迎使用 TipTap 编辑器</h1>
      <p>这是一个功能丰富的富文本编辑器，支持：</p>
      <ul>
        <li><strong>粗体</strong>和<em>斜体</em>文本</li>
        <li><s>删除线</s>和<code>行内代码</code></li>
        <li>多级标题</li>
        <li>有序和无序列表</li>
      </ul>
      <blockquote>
        <p>这是一个引用块，用于突出显示重要内容。</p>
      </blockquote>
      <hr>
      <p>试试选择文本来使用气泡菜单！</p>
    `;
    setContent(sampleContent);
  };

  return (
    <div className="mx-auto max-w-4xl space-y-4 p-6">
      <h1 className="font-bold text-2xl text-gray-900">TipTap 编辑器示例</h1>

      <div className="mb-4 flex gap-2">
        <Button onClick={handleSave} variant="default">
          保存内容
        </Button>
        <Button onClick={handleClear} variant="outline">
          清空内容
        </Button>
        <Button onClick={handleLoadSample} variant="outline">
          加载示例
        </Button>
      </div>

      <TiptapEditor
        ref={editorRef}
        value={content}
        onChange={setContent}
        placeholder="开始写作..."
        className="min-h-[400px]"
        showToolbar={true}
        editable={true}
      />

      <div className="mt-4 rounded-lg bg-gray-50 p-4">
        <h3 className="mb-2 font-semibold">实时 HTML 预览：</h3>
        <pre className="overflow-x-auto whitespace-pre-wrap text-gray-600 text-sm">
          {content}
        </pre>
      </div>
    </div>
  );
}
