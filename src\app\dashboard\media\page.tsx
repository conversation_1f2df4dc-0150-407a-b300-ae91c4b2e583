"use client";

import { api } from "@convex/api";
import { useMutation, useQuery } from "convex/react";
import {
  Calendar,
  Download,
  Eye,
  FileText,
  FolderOpen,
  Grid3X3,
  Image,
  List,
  MoreHorizontal,
  Music,
  Search,
  Trash2,
  Upload,
  Video,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";

type FileCategory = "all" | "image" | "video" | "document" | "audio" | "other";
type ViewMode = "grid" | "list";

const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

const SUPPORTED_FORMATS = {
  image: [".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg"],
  video: [".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm"],
  document: [".pdf", ".doc", ".docx", ".txt", ".rtf"],
  audio: [".mp3", ".wav", ".ogg", ".aac"],
};

export default function MediaLibraryPage() {
  const t = useTranslations("media");
  const [selectedCategory, setSelectedCategory] = useState<FileCategory>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [isUploading, setIsUploading] = useState(false);

  // Queries and mutations
  const mediaFiles = useQuery(api.media.getMediaFiles, {
    category: selectedCategory,
    searchQuery: searchQuery || undefined,
    limit: 50,
  });
  const mediaStats = useQuery(api.media.getMediaStats);
  const generateUploadUrl = useMutation(api.media.generateUploadUrl);
  const saveFileInfo = useMutation(api.media.saveFileInfo);
  const deleteMediaFile = useMutation(api.media.deleteMediaFile);

  // File upload handling
  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return;

      setIsUploading(true);
      toast.loading(t("upload.uploading"));

      try {
        for (const file of acceptedFiles) {
          // Validate file size
          if (file.size > MAX_FILE_SIZE) {
            toast.error(`${file.name}: ${t("messages.file_too_large")}`);
            continue;
          }

          // Generate upload URL
          const uploadUrl = await generateUploadUrl();

          // Upload file
          const uploadResponse = await fetch(uploadUrl, {
            method: "POST",
            headers: { "Content-Type": file.type },
            body: file,
          });

          if (!uploadResponse.ok) {
            throw new Error(`Upload failed for ${file.name}`);
          }

          const { storageId } = await uploadResponse.json();

          // Determine category based on file type
          let category: "image" | "video" | "document" | "audio" | "other" =
            "other";
          if (file.type.startsWith("image/")) {
            category = "image";
          } else if (file.type.startsWith("video/")) {
            category = "video";
          } else if (file.type.startsWith("audio/")) {
            category = "audio";
          } else if (
            file.type === "application/pdf" ||
            file.type.includes("document") ||
            file.type.includes("text/")
          ) {
            category = "document";
          }

          // Save file info to database
          await saveFileInfo({
            storageId,
            originalName: file.name,
            size: file.size,
            contentType: file.type,
            category,
            isPublic: true,
          });
        }

        toast.dismiss();
        toast.success(t("upload.upload_success"));
      } catch (error) {
        console.error("Upload error:", error);
        toast.dismiss();
        toast.error(t("upload.upload_error"));
      } finally {
        setIsUploading(false);
      }
    },
    [generateUploadUrl, saveFileInfo, t],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    disabled: isUploading,
    multiple: true,
  });

  // File actions
  const handleDeleteFile = async (fileId: string) => {
    try {
      await deleteMediaFile({ fileId: fileId as any });
      toast.success(t("messages.delete_success"));
    } catch (error) {
      console.error("Delete error:", error);
      toast.error(t("messages.delete_error"));
    }
  };

  const handleCopyUrl = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      toast.success(t("messages.copy_url_success"));
    } catch (error) {
      console.error("Copy URL error:", error);
    }
  };

  // Utility functions
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  };

  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith("image/")) return <Image className="h-4 w-4" />;
    if (contentType.startsWith("video/")) return <Video className="h-4 w-4" />;
    if (contentType.startsWith("audio/")) return <Music className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  const getCategoryBadgeColor = (category: string) => {
    switch (category) {
      case "image":
        return "bg-green-100 text-green-800";
      case "video":
        return "bg-blue-100 text-blue-800";
      case "document":
        return "bg-orange-100 text-orange-800";
      case "audio":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="font-bold text-2xl text-gray-900">{t("title")}</h1>
          <p className="text-gray-600 text-sm">{t("description")}</p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
          >
            {viewMode === "grid" ? (
              <List className="h-4 w-4" />
            ) : (
              <Grid3X3 className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Statistics */}
      {mediaStats && (
        <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-6">
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <p className="font-medium text-2xl">{mediaStats.totalFiles}</p>
                <p className="text-muted-foreground text-sm">
                  {t("stats.total_files")}
                </p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <p className="font-medium text-2xl">
                  {formatFileSize(mediaStats.totalSize)}
                </p>
                <p className="text-muted-foreground text-sm">
                  {t("stats.total_size")}
                </p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <p className="font-medium text-2xl">
                  {mediaStats.categoryStats.image}
                </p>
                <p className="text-muted-foreground text-sm">
                  {t("stats.images")}
                </p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <p className="font-medium text-2xl">
                  {mediaStats.categoryStats.video}
                </p>
                <p className="text-muted-foreground text-sm">
                  {t("stats.videos")}
                </p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <p className="font-medium text-2xl">
                  {mediaStats.categoryStats.document}
                </p>
                <p className="text-muted-foreground text-sm">
                  {t("stats.documents")}
                </p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <p className="font-medium text-2xl">
                  {mediaStats.categoryStats.audio}
                </p>
                <p className="text-muted-foreground text-sm">
                  {t("stats.audio")}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            {t("upload.title")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={`rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
              isDragActive
                ? "border-primary bg-primary/5"
                : "border-gray-300 hover:border-primary/50"
            } ${isUploading ? "pointer-events-none opacity-50" : "cursor-pointer"}`}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-4 font-medium text-lg">
              {isDragActive ? "放下文件即可上传" : t("upload.drag_drop")}
            </p>
            <p className="mt-2 text-gray-500 text-sm">
              {t("upload.max_size", { size: "50" })}
            </p>
            <p className="mt-1 text-gray-500 text-sm">
              {t("upload.supported_formats", {
                formats: Object.values(SUPPORTED_FORMATS).flat().join(", "),
              })}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory("all")}
              >
                {t("filters.all")}
              </Button>
              <Button
                variant={selectedCategory === "image" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory("image")}
              >
                <Image className="mr-2 h-4 w-4" />
                {t("filters.images")}
              </Button>
              <Button
                variant={selectedCategory === "video" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory("video")}
              >
                <Video className="mr-2 h-4 w-4" />
                {t("filters.videos")}
              </Button>
              <Button
                variant={
                  selectedCategory === "document" ? "default" : "outline"
                }
                size="sm"
                onClick={() => setSelectedCategory("document")}
              >
                <FileText className="mr-2 h-4 w-4" />
                {t("filters.documents")}
              </Button>
              <Button
                variant={selectedCategory === "audio" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory("audio")}
              >
                <Music className="mr-2 h-4 w-4" />
                {t("filters.audio")}
              </Button>
            </div>

            <div className="relative w-full md:w-64">
              <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder={t("filters.search_placeholder")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* File Grid/List */}
      {mediaFiles && mediaFiles.length > 0 ? (
        <div
          className={
            viewMode === "grid"
              ? "grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
              : "space-y-2"
          }
        >
          {mediaFiles.map((file) => (
            <Card key={file._id} className="overflow-hidden">
              <CardContent className="p-4">
                {viewMode === "grid" ? (
                  // Grid View
                  <div className="space-y-3">
                    {file.category === "image" && file.url ? (
                      <div className="aspect-video overflow-hidden rounded-lg bg-gray-100">
                        <img
                          src={file.url}
                          alt={file.altText || file.originalName}
                          className="h-full w-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="flex aspect-video items-center justify-center rounded-lg bg-gray-100">
                        {getFileIcon(file.contentType)}
                      </div>
                    )}

                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h3 className="truncate pr-2 font-medium text-sm">
                          {file.originalName}
                        </h3>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {file.url && (
                              <DropdownMenuItem
                                onClick={() => window.open(file.url, "_blank")}
                              >
                                <Eye className="mr-2 h-4 w-4" />
                                {t("actions.view")}
                              </DropdownMenuItem>
                            )}
                            {file.url && (
                              <DropdownMenuItem
                                onClick={() => handleCopyUrl(file.url!)}
                              >
                                <Download className="mr-2 h-4 w-4" />
                                {t("actions.copy_url")}
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteFile(file._id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              {t("actions.delete")}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <div className="flex items-center justify-between">
                        <Badge className={getCategoryBadgeColor(file.category)}>
                          {t(
                            `filters.${file.category === "document" ? "documents" : `${file.category}s`}`,
                          )}
                        </Badge>
                        <span className="text-gray-500 text-xs">
                          {formatFileSize(file.size)}
                        </span>
                      </div>

                      <div className="flex items-center text-gray-500 text-xs">
                        <Calendar className="mr-1 h-3 w-3" />
                        {new Date(file._creationTime).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ) : (
                  // List View
                  <div className="flex items-center gap-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded bg-gray-100">
                      {getFileIcon(file.contentType)}
                    </div>

                    <div className="min-w-0 flex-1">
                      <h3 className="truncate font-medium text-sm">
                        {file.originalName}
                      </h3>
                      <div className="flex items-center gap-4 text-gray-500 text-xs">
                        <span>{formatFileSize(file.size)}</span>
                        <span>
                          {new Date(file._creationTime).toLocaleDateString()}
                        </span>
                        <Badge className={getCategoryBadgeColor(file.category)}>
                          {t(
                            `filters.${file.category === "document" ? "documents" : `${file.category}s`}`,
                          )}
                        </Badge>
                      </div>
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {file.url && (
                          <DropdownMenuItem
                            onClick={() => window.open(file.url, "_blank")}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            {t("actions.view")}
                          </DropdownMenuItem>
                        )}
                        {file.url && (
                          <DropdownMenuItem
                            onClick={() => handleCopyUrl(file.url!)}
                          >
                            <Download className="mr-2 h-4 w-4" />
                            {t("actions.copy_url")}
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteFile(file._id)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          {t("actions.delete")}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="py-12 text-center">
            <FolderOpen className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 font-medium text-lg">
              {t("messages.no_files")}
            </h3>
            <p className="mt-2 text-gray-500 text-sm">
              {t("messages.no_files_description")}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
