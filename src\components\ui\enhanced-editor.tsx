"use client";

import Placeholder from "@tiptap/extension-placeholder";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>u, EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import {
  Bold,
  Code,
  Heading1,
  Heading2,
  Heading3,
  Image as ImageIcon,
  Italic,
  List,
  ListOrdered,
  Minus,
  Quote,
  Redo,
  Strikethrough,
  Type,
  Undo,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { forwardRef, useImperativeHandle, useState } from "react";
import { Button } from "@/components/ui/button";
import { MediaPicker } from "@/components/ui/media-picker";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { ResizableImage } from "./resizable-image-extension";

interface EnhancedEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  editable?: boolean;
  showToolbar?: boolean;
  enableMediaPicker?: boolean;
}

export interface EnhancedEditorRef {
  getHTML: () => string;
  setContent: (content: string) => void;
  focus: () => void;
  clear: () => void;
}

const EnhancedEditor = forwardRef<EnhancedEditorRef, EnhancedEditorProps>(
  (
    {
      value = "",
      onChange,
      placeholder,
      className,
      editable = true,
      showToolbar = true,
      enableMediaPicker = true,
    },
    ref,
  ) => {
    const t = useTranslations("editor");
    const defaultPlaceholder = placeholder || t("placeholder");
    const [isMediaPickerOpen, setIsMediaPickerOpen] = useState(false);

    const editor = useEditor({
      extensions: [
        StarterKit.configure({
          bulletList: {
            keepMarks: true,
            keepAttributes: false,
          },
          orderedList: {
            keepMarks: true,
            keepAttributes: false,
          },
        }),
        ResizableImage.configure({
          inline: false,
          allowBase64: false,
          HTMLAttributes: {
            class: "rounded-lg my-4",
          },
        }),
        Placeholder.configure({
          placeholder: defaultPlaceholder,
        }),
      ],
      content: value,
      editable,
      onUpdate: ({ editor }) => {
        const html = editor.getHTML();
        if (onChange) {
          onChange(html);
        }
      },
      editorProps: {
        attributes: {
          class: cn(
            // 基础样式
            "mx-auto max-w-none focus:outline-none",
            "text-gray-900 text-sm leading-relaxed",
            "min-h-[200px] px-6 py-4",
            // 标题样式
            "[&_h1]:mt-6 [&_h1]:mb-4 [&_h1]:font-bold [&_h1]:text-2xl [&_h1]:text-gray-900 [&_h1]:leading-tight",
            "[&_h2]:mt-5 [&_h2]:mb-3 [&_h2]:font-bold [&_h2]:text-gray-900 [&_h2]:text-xl [&_h2]:leading-tight",
            "[&_h3]:mt-4 [&_h3]:mb-2 [&_h3]:font-bold [&_h3]:text-gray-900 [&_h3]:text-lg [&_h3]:leading-tight",
            // 段落样式
            "[&_p]:mb-4 [&_p]:text-gray-700 [&_p]:leading-relaxed",
            "[&_p:first-child]:mt-0 [&_p:last-child]:mb-0",
            // 链接样式
            "[&_a]:text-blue-600 [&_a]:no-underline hover:[&_a]:underline",
            // 强调样式
            "[&_strong]:font-semibold [&_strong]:text-gray-900",
            "[&_em]:italic",
            "[&_s]:text-gray-500 [&_s]:line-through",
            // 代码样式
            "[&_code]:rounded [&_code]:bg-red-50 [&_code]:px-1 [&_code]:py-0.5 [&_code]:font-mono [&_code]:text-red-600 [&_code]:text-sm",
            "[&_pre]:overflow-x-auto [&_pre]:rounded [&_pre]:bg-gray-900 [&_pre]:p-3 [&_pre]:font-mono [&_pre]:text-gray-100 [&_pre]:text-sm",
            "[&_pre_code]:bg-transparent [&_pre_code]:p-0 [&_pre_code]:text-gray-100",
            // 引用样式
            "[&_blockquote]:my-4 [&_blockquote]:border-gray-300 [&_blockquote]:border-l-4 [&_blockquote]:py-2 [&_blockquote]:pl-4 [&_blockquote]:text-gray-600 [&_blockquote]:italic",
            // 列表样式
            "[&_ul]:mb-4 [&_ul]:list-outside [&_ul]:list-disc [&_ul]:space-y-1 [&_ul]:pl-6",
            "[&_ol]:mb-4 [&_ol]:list-outside [&_ol]:list-decimal [&_ol]:space-y-1 [&_ol]:pl-6",
            "[&_li]:pl-1 [&_li]:text-gray-700 [&_li]:leading-relaxed",
            "[&_ul_ul]:mt-2 [&_ul_ul]:mb-0 [&_ul_ul]:ml-4 [&_ul_ul]:pl-4",
            "[&_ol_ol]:mt-2 [&_ol_ol]:mb-0 [&_ol_ol]:ml-4 [&_ol_ol]:pl-4",
            // 分隔线样式
            "[&_hr]:my-6 [&_hr]:border-0 [&_hr]:border-gray-300 [&_hr]:border-t",
            // 图片样式 - 支持多种对齐和大小，默认不占满宽度
            "[&_img]:my-4 [&_img]:max-w-none [&_img]:rounded-lg",
            "left']]:mr-auto [&_img[style*='text-align:",
            "center']]:mx-auto [&_img[style*='text-align:",
            "right']]:ml-auto [&_img[style*='text-align:",
            "left']]:float-left left']]:mr-4 left']]:mb-2 [&_img[style*='float: [&_img[style*='float: [&_img[style*='float:",
            "right']]:float-right right']]:ml-4 right']]:mb-2 [&_img[style*='float: [&_img[style*='float: [&_img[style*='float:",
          ),
        },
      },
    });

    useImperativeHandle(ref, () => ({
      getHTML: () => editor?.getHTML() || "",
      setContent: (content: string) => {
        if (editor) {
          editor.commands.setContent(content, false);
        }
      },
      focus: () => {
        if (editor) {
          editor.commands.focus();
        }
      },
      clear: () => {
        if (editor) {
          editor.commands.clearContent();
        }
      },
    }));

    const handleMediaSelect = (file: {
      url: string | null;
      category: string;
      originalName: string;
      altText?: string;
    }) => {
      if (editor && file.url) {
        if (file.category === "image") {
          // 使用可调整大小的图片
          editor
            .chain()
            .focus()
            .setResizableImage({
              src: file.url,
              alt: file.altText || file.originalName,
              width: 400,
              height: 300,
            })
            .run();
        } else {
          // For non-image files, insert as a link
          const linkText = file.originalName;
          editor
            .chain()
            .focus()
            .insertContent(
              `<p><a href="${file.url}" target="_blank" rel="noopener noreferrer">${linkText}</a></p>`,
            )
            .run();
        }
      }
    };

    // 图片工具栏功能
    const _updateImageAttribute = (attribute: string, value: string) => {
      if (!editor) return;

      const { selection } = editor.state;
      const node = editor.state.doc.nodeAt(selection.from);

      if (node && node.type.name === "image") {
        const _currentAttrs = node.attrs;
        let newStyle = "";
        let newClass = "";

        // 根据属性类型设置样式
        if (attribute === "size") {
          switch (value) {
            case "small":
              newStyle =
                "max-width: 300px; height: auto; display: block; margin: 16px auto;";
              newClass = "img-size-small";
              break;
            case "medium":
              newStyle =
                "max-width: 500px; height: auto; display: block; margin: 16px auto;";
              newClass = "img-size-medium";
              break;
            case "large":
              newStyle =
                "max-width: 800px; height: auto; display: block; margin: 16px auto;";
              newClass = "img-size-large";
              break;
            case "full":
              newStyle =
                "max-width: 100%; height: auto; display: block; margin: 16px auto;";
              newClass = "img-size-full";
              break;
          }
        } else if (attribute === "align") {
          const currentSize = getCurrentImageSize();
          const sizeStyle = getSizeStyle(currentSize);

          switch (value) {
            case "left":
              newStyle = `${sizeStyle} text-align: left; display: block; margin: 16px auto 16px 0;`;
              newClass = `img-size-${currentSize} img-align-left`;
              break;
            case "center":
              newStyle = `${sizeStyle} text-align: center; display: block; margin: 16px auto;`;
              newClass = `img-size-${currentSize} img-align-center`;
              break;
            case "right":
              newStyle = `${sizeStyle} text-align: right; display: block; margin: 16px 0 16px auto;`;
              newClass = `img-size-${currentSize} img-align-right`;
              break;
          }
        } else if (attribute === "float") {
          const currentSize = getCurrentImageSize();
          const sizeStyle = getSizeStyle(currentSize);

          switch (value) {
            case "left":
              newStyle = `${sizeStyle} float: left; margin: 0 16px 8px 0;`;
              newClass = `img-size-${currentSize} img-float-left`;
              break;
            case "right":
              newStyle = `${sizeStyle} float: right; margin: 0 0 8px 16px;`;
              newClass = `img-size-${currentSize} img-float-right`;
              break;
            case "none":
              newStyle = `${sizeStyle} display: block; margin: 16px auto; float: none;`;
              newClass = `img-size-${currentSize} img-float-none`;
              break;
          }
        }

        // 更新图片属性
        editor
          .chain()
          .focus()
          .updateAttributes("image", {
            style: newStyle,
            class: newClass,
            "data-size": attribute === "size" ? value : getCurrentImageSize(),
            "data-align":
              attribute === "align" ? value : getCurrentImageAlign(),
            "data-float":
              attribute === "float" ? value : getCurrentImageFloat(),
          })
          .run();
      }
    };

    const getSizeStyle = (size: string) => {
      switch (size) {
        case "small":
          return "max-width: 300px; height: auto;";
        case "medium":
          return "max-width: 500px; height: auto;";
        case "large":
          return "max-width: 800px; height: auto;";
        case "full":
          return "max-width: 100%; height: auto;";
        default:
          return "max-width: 100%; height: auto;";
      }
    };

    const getCurrentImageAlign = () => {
      if (!editor) return "center";

      const { selection } = editor.state;
      const node = editor.state.doc.nodeAt(selection.from);

      if (node && node.type.name === "image") {
        const dataAlign = node.attrs["data-align"];
        if (dataAlign) return dataAlign;

        const style = node.attrs.style || "";
        const className = node.attrs.class || "";

        if (
          style.includes("float: left") ||
          className.includes("img-float-left")
        )
          return "float-left";
        if (
          style.includes("float: right") ||
          className.includes("img-float-right")
        )
          return "float-right";
        if (
          style.includes("text-align: left") ||
          className.includes("img-align-left")
        )
          return "left";
        if (
          style.includes("text-align: right") ||
          className.includes("img-align-right")
        )
          return "right";
        return "center";
      }
      return "center";
    };

    const getCurrentImageSize = () => {
      if (!editor) return "full";

      const { selection } = editor.state;
      const node = editor.state.doc.nodeAt(selection.from);

      if (node && node.type.name === "image") {
        const dataSize = node.attrs["data-size"];
        if (dataSize) return dataSize;

        const style = node.attrs.style || "";
        const className = node.attrs.class || "";

        if (
          style.includes("max-width: 300px") ||
          className.includes("img-size-small")
        )
          return "small";
        if (
          style.includes("max-width: 500px") ||
          className.includes("img-size-medium")
        )
          return "medium";
        if (
          style.includes("max-width: 800px") ||
          className.includes("img-size-large")
        )
          return "large";
        return "full";
      }
      return "full";
    };

    const getCurrentImageFloat = () => {
      if (!editor) return "none";

      const { selection } = editor.state;
      const node = editor.state.doc.nodeAt(selection.from);

      if (node && node.type.name === "image") {
        const dataFloat = node.attrs["data-float"];
        if (dataFloat) return dataFloat;

        const style = node.attrs.style || "";
        const className = node.attrs.class || "";

        if (
          style.includes("float: left") ||
          className.includes("img-float-left")
        )
          return "left";
        if (
          style.includes("float: right") ||
          className.includes("img-float-right")
        )
          return "right";
        return "none";
      }
      return "none";
    };

    if (!editor) {
      return null;
    }

    const ToolbarButton = ({
      onClick,
      disabled,
      isActive,
      children,
      title,
    }: {
      onClick: () => void;
      disabled?: boolean;
      isActive?: boolean;
      children: React.ReactNode;
      title?: string;
    }) => (
      <Button
        size="sm"
        variant={isActive ? "default" : "ghost"}
        onMouseDown={(e) => {
          e.preventDefault(); // 防止失去焦点
        }}
        onClick={(e) => {
          e.preventDefault();
          onClick();
        }}
        disabled={disabled}
        title={title}
        className="h-8 w-8 p-0"
      >
        {children}
      </Button>
    );

    return (
      <>
        <div
          className={cn(
            "overflow-hidden rounded-lg border border-gray-200 bg-white",
            className,
          )}
        >
          {showToolbar && (
            <div className="border-gray-200 border-b bg-gray-50 p-2">
              <div className="flex flex-wrap items-center gap-1">
                {/* Text Formatting */}
                <ToolbarButton
                  onClick={() => editor.chain().focus().toggleBold().run()}
                  disabled={!editor.can().chain().focus().toggleBold().run()}
                  isActive={editor.isActive("bold")}
                  title={t("toolbar.bold")}
                >
                  <Bold className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() => editor.chain().focus().toggleItalic().run()}
                  disabled={!editor.can().chain().focus().toggleItalic().run()}
                  isActive={editor.isActive("italic")}
                  title={t("toolbar.italic")}
                >
                  <Italic className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() => editor.chain().focus().toggleStrike().run()}
                  disabled={!editor.can().chain().focus().toggleStrike().run()}
                  isActive={editor.isActive("strike")}
                  title={t("toolbar.strikethrough")}
                >
                  <Strikethrough className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() => editor.chain().focus().toggleCode().run()}
                  disabled={!editor.can().chain().focus().toggleCode().run()}
                  isActive={editor.isActive("code")}
                  title={t("toolbar.code")}
                >
                  <Code className="h-4 w-4" />
                </ToolbarButton>

                <Separator orientation="vertical" className="h-6" />

                {/* Headings */}
                <ToolbarButton
                  onClick={() => editor.chain().focus().setParagraph().run()}
                  isActive={editor.isActive("paragraph")}
                  title={t("toolbar.paragraph")}
                >
                  <Type className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().toggleHeading({ level: 1 }).run()
                  }
                  isActive={editor.isActive("heading", { level: 1 })}
                  title={t("toolbar.heading1")}
                >
                  <Heading1 className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().toggleHeading({ level: 2 }).run()
                  }
                  isActive={editor.isActive("heading", { level: 2 })}
                  title={t("toolbar.heading2")}
                >
                  <Heading2 className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().toggleHeading({ level: 3 }).run()
                  }
                  isActive={editor.isActive("heading", { level: 3 })}
                  title={t("toolbar.heading3")}
                >
                  <Heading3 className="h-4 w-4" />
                </ToolbarButton>

                <Separator orientation="vertical" className="h-6" />

                {/* Lists */}
                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().toggleBulletList().run()
                  }
                  isActive={editor.isActive("bulletList")}
                  title={t("toolbar.bullet_list")}
                >
                  <List className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().toggleOrderedList().run()
                  }
                  isActive={editor.isActive("orderedList")}
                  title={t("toolbar.ordered_list")}
                >
                  <ListOrdered className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().toggleBlockquote().run()
                  }
                  isActive={editor.isActive("blockquote")}
                  title={t("toolbar.blockquote")}
                >
                  <Quote className="h-4 w-4" />
                </ToolbarButton>

                {/* Media */}
                {enableMediaPicker && (
                  <>
                    <Separator orientation="vertical" className="h-6" />
                    <ToolbarButton
                      onClick={() => setIsMediaPickerOpen(true)}
                      title={t("toolbar.insert_media")}
                    >
                      <ImageIcon className="h-4 w-4" />
                    </ToolbarButton>
                  </>
                )}

                <Separator orientation="vertical" className="h-6" />

                {/* History */}
                <ToolbarButton
                  onClick={() => editor.chain().focus().undo().run()}
                  disabled={!editor.can().chain().focus().undo().run()}
                  title={t("toolbar.undo")}
                >
                  <Undo className="h-4 w-4" />
                </ToolbarButton>

                <ToolbarButton
                  onClick={() => editor.chain().focus().redo().run()}
                  disabled={!editor.can().chain().focus().redo().run()}
                  title={t("toolbar.redo")}
                >
                  <Redo className="h-4 w-4" />
                </ToolbarButton>

                <Separator orientation="vertical" className="h-6" />

                {/* Advanced */}
                <ToolbarButton
                  onClick={() =>
                    editor.chain().focus().setHorizontalRule().run()
                  }
                  title={t("toolbar.horizontal_rule")}
                >
                  <Minus className="h-4 w-4" />
                </ToolbarButton>
              </div>
            </div>
          )}

          <div className="relative">
            <EditorContent editor={editor} />

            {editor && (
              <>
                {/* 文本选择时的工具栏 */}
                <BubbleMenu
                  editor={editor}
                  tippyOptions={{ duration: 100 }}
                  shouldShow={({ state, from, to }) => {
                    const { doc, selection } = state;
                    const { empty } = selection;

                    // 不显示在图片上
                    if (!empty) {
                      const node = doc.nodeAt(from);
                      if (node && node.type.name === "image") {
                        return false;
                      }
                    }

                    return !empty;
                  }}
                  className="flex gap-1 rounded-lg border border-gray-200 bg-white p-1 shadow-md"
                >
                  <Button
                    size="sm"
                    variant={editor.isActive("bold") ? "default" : "ghost"}
                    onMouseDown={(e) => e.preventDefault()}
                    onClick={() => editor.chain().focus().toggleBold().run()}
                    className="h-8 w-8 p-0"
                    title={t("toolbar.bold")}
                  >
                    <Bold className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant={editor.isActive("italic") ? "default" : "ghost"}
                    onMouseDown={(e) => e.preventDefault()}
                    onClick={() => editor.chain().focus().toggleItalic().run()}
                    className="h-8 w-8 p-0"
                    title={t("toolbar.italic")}
                  >
                    <Italic className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant={editor.isActive("strike") ? "default" : "ghost"}
                    onMouseDown={(e) => e.preventDefault()}
                    onClick={() => editor.chain().focus().toggleStrike().run()}
                    className="h-8 w-8 p-0"
                    title={t("toolbar.strikethrough")}
                  >
                    <Strikethrough className="h-4 w-4" />
                  </Button>
                </BubbleMenu>

                {/* 可调整大小的图片已经集成了控制功能，不需要BubbleMenu */}
              </>
            )}
          </div>
        </div>

        {/* Media Picker Dialog */}
        {enableMediaPicker && (
          <MediaPicker
            open={isMediaPickerOpen}
            onOpenChange={setIsMediaPickerOpen}
            onSelect={handleMediaSelect}
            allowedTypes={["all"]}
            multiple={false}
          />
        )}
      </>
    );
  },
);

EnhancedEditor.displayName = "EnhancedEditor";

export { EnhancedEditor };
