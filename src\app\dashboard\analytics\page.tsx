"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  TrendingUp,
  TrendingDown,
  Users,
  Eye,
  MousePointer,
  DollarSign,
} from "lucide-react";

const analyticsData = [
  {
    title: "Page Views",
    value: "12,345",
    change: "+12.5%",
    trend: "up",
    icon: Eye,
  },
  {
    title: "Unique Visitors",
    value: "8,432",
    change: "+8.2%",
    trend: "up",
    icon: Users,
  },
  {
    title: "Click Rate",
    value: "3.2%",
    change: "-0.5%",
    trend: "down",
    icon: MousePointer,
  },
  {
    title: "Revenue",
    value: "$23,456",
    change: "+15.3%",
    trend: "up",
    icon: DollarSign,
  },
];

const topPages = [
  { page: "/dashboard", views: 2345, percentage: 89 },
  { page: "/dashboard/users", views: 1892, percentage: 72 },
  { page: "/dashboard/settings", views: 1234, percentage: 47 },
  { page: "/dashboard/analytics", views: 876, percentage: 33 },
];

export default function AnalyticsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="font-bold text-2xl tracking-tight">Analytics</h2>
          <p className="text-muted-foreground">
            Monitor your application performance and user engagement.
          </p>
        </div>
        <Button variant="outline">Export Data</Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {analyticsData.map((item) => (
          <Card key={item.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="font-medium text-sm">
                {item.title}
              </CardTitle>
              <item.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="font-bold text-2xl">{item.value}</div>
              <div className="flex items-center text-muted-foreground text-xs">
                {item.trend === "up" ? (
                  <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span
                  className={
                    item.trend === "up" ? "text-green-500" : "text-red-500"
                  }
                >
                  {item.change}
                </span>
                <span className="ml-1">from last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Top Pages</CardTitle>
            <CardDescription>Most visited pages this month</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {topPages.map((page) => (
              <div key={page.page} className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">{page.page}</span>
                  <span className="text-muted-foreground">
                    {page.views} views
                  </span>
                </div>
                <Progress value={page.percentage} className="h-2" />
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>User Activity</CardTitle>
            <CardDescription>Recent user interactions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                  <span className="text-sm">New user registration</span>
                </div>
                <span className="text-muted-foreground text-xs">2 min ago</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-green-500"></div>
                  <span className="text-sm">Page view: /dashboard/users</span>
                </div>
                <span className="text-muted-foreground text-xs">5 min ago</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                  <span className="text-sm">Settings updated</span>
                </div>
                <span className="text-muted-foreground text-xs">
                  10 min ago
                </span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-purple-500"></div>
                  <span className="text-sm">Data export completed</span>
                </div>
                <span className="text-muted-foreground text-xs">
                  15 min ago
                </span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-red-500"></div>
                  <span className="text-sm">Failed login attempt</span>
                </div>
                <span className="text-muted-foreground text-xs">
                  20 min ago
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
          <CardDescription>System performance over time</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <div className="font-medium text-sm">Response Time</div>
              <div className="font-bold text-2xl">245ms</div>
              <Progress value={75} className="h-2" />
              <div className="text-muted-foreground text-xs">
                Target: &lt;200ms
              </div>
            </div>

            <div className="space-y-2">
              <div className="font-medium text-sm">Uptime</div>
              <div className="font-bold text-2xl">99.9%</div>
              <Progress value={99.9} className="h-2" />
              <div className="text-muted-foreground text-xs">Last 30 days</div>
            </div>

            <div className="space-y-2">
              <div className="font-medium text-sm">Error Rate</div>
              <div className="font-bold text-2xl">0.1%</div>
              <Progress value={10} className="h-2" />
              <div className="text-muted-foreground text-xs">
                Target: &lt;1%
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
