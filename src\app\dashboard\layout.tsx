"use client";

import { api } from "@convex/api";
import { useAuthActions } from "@convex-dev/auth/react";
import { useQuery } from "convex/react";
import {
  BarChart3,
  Building2,
  ChevronDown,
  ChevronRight,
  FileText,
  FolderOpen,
  LogOut,
  PenTool,
  Settings,
  Sparkles,
  User,
  UserCog,
  Zap,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { SmartBreadcrumb, useBreadcrumb } from "@/components/ui/breadcrumb";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar";

type SubItem = {
  title: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
};

type PlatformItem = {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  url: string;
  badge?: string;
  subItems?: SubItem[];
};

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const currentUser = useQuery(api.users.current);
  const { signOut } = useAuthActions();
  const t = useTranslations("dashboard");
  const pathname = usePathname();

  // Fix SSR hydration issue by deferring pathname-based logic
  const [isClient, setIsClient] = useState(false);

  // Persistent sidebar state with localStorage
  const [collapsibleStates, setCollapsibleStates] = useState<
    Record<string, boolean>
  >({});

  useEffect(() => {
    setIsClient(true);

    // Load saved collapsible states from localStorage
    try {
      const savedStates = localStorage.getItem("sidebar-collapsible-states");
      if (savedStates) {
        setCollapsibleStates(JSON.parse(savedStates));
      }
    } catch (error) {
      console.warn("Failed to load sidebar states:", error);
    }
  }, []);

  // Save collapsible state to localStorage
  const updateCollapsibleState = (itemTitle: string, isOpen: boolean) => {
    const newStates = { ...collapsibleStates, [itemTitle]: isOpen };
    setCollapsibleStates(newStates);

    try {
      localStorage.setItem(
        "sidebar-collapsible-states",
        JSON.stringify(newStates),
      );
    } catch (error) {
      console.warn("Failed to save sidebar state:", error);
    }
  };

  // Generate breadcrumb items
  const breadcrumbItems = useBreadcrumb(pathname);

  // Helper function to check if path is active (only on client)
  const isPathActive = (itemUrl: string, currentPath: string) => {
    if (!isClient) return false; // Prevent SSR mismatch

    // Exact match for root paths
    if (itemUrl === currentPath) return true;

    // For parent paths with subItems, check if current path starts with item path
    if (currentPath.startsWith(`${itemUrl}/`) && itemUrl !== "/dashboard") {
      return true;
    }

    return false;
  };

  const platformItems: PlatformItem[] = [
    {
      title: t("navigation.analytics"),
      icon: BarChart3,
      url: "/dashboard/analytics",
      badge: t("badges.new"),
    },
    {
      title: t("navigation.news"),
      icon: FileText,
      url: "/dashboard/news",
      subItems: [
        {
          title: t("news.list"),
          url: "/dashboard/news",
          icon: FileText,
        },
        {
          title: t("news.categories"),
          url: "/dashboard/news/categories",
          icon: FolderOpen,
        },
        {
          title: t("news.create"),
          url: "/dashboard/news/create",
          icon: PenTool,
        },
      ],
    },
    {
      title: t("navigation.products"),
      icon: Building2,
      url: "/dashboard/products",
      subItems: [
        {
          title: t("products.list"),
          url: "/dashboard/products",
          icon: Building2,
        },
        {
          title: t("products.categories"),
          url: "/dashboard/products/categories",
          icon: FolderOpen,
        },
        {
          title: t("products.create"),
          url: "/dashboard/products/create",
          icon: PenTool,
        },
      ],
    },
    {
      title: t("navigation.media"),
      icon: FolderOpen,
      url: "/dashboard/media",
    },
    {
      title: t("navigation.models"),
      icon: Sparkles,
      url: "/dashboard/models",
      badge: "12",
    },
    {
      title: t("navigation.users"),
      icon: User,
      url: "/dashboard/users",
    },
    {
      title: t("navigation.settings"),
      icon: Settings,
      url: "/dashboard/settings",
    },
  ];

  // Get user initials for avatar fallback
  const getUserInitials = (name?: string, email?: string) => {
    if (name) {
      return name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email[0].toUpperCase();
    }
    return "U";
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  return (
    <SidebarProvider>
      <Sidebar className="border-border/40 border-r bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <SidebarHeader className="border-border/40 border-b px-6 py-6">
          <div className="flex items-center gap-4">
            <div className="relative">
              <div className="flex h-11 w-11 items-center justify-center rounded-2xl bg-gradient-to-br from-primary via-primary to-primary/80 shadow-lg ring-1 ring-primary/20">
                <Building2 className="h-5 w-5 text-primary-foreground" />
              </div>
              {/* 装饰性光点 */}
              <div className="-top-1 -right-1 absolute h-3 w-3 rounded-full bg-gradient-to-br from-green-400 to-green-500 shadow-lg ring-2 ring-background"></div>
            </div>
            <div className="flex flex-col">
              <h2 className="font-bold text-base text-foreground leading-tight">
                {t("company.name")}
              </h2>
              <div className="mt-1 flex items-center gap-2">
                <span className="font-medium text-muted-foreground text-xs">
                  {t("company.plan")}
                </span>
                <Badge
                  variant="secondary"
                  className="h-5 border-0 bg-gradient-to-r from-purple-100 to-pink-100 px-2 py-0 font-semibold text-[10px] text-purple-800"
                >
                  <Sparkles className="mr-1 h-2.5 w-2.5" />
                  {t("badges.pro")}
                </Badge>
              </div>
            </div>
          </div>
        </SidebarHeader>

        <SidebarContent className="px-4 py-6">
          {/* Platform Section */}
          <SidebarGroup>
            <SidebarGroupLabel className="mb-4 px-3 py-2 font-bold text-[11px] text-muted-foreground uppercase tracking-wider">
              {t("navigation.platform")}
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className="space-y-2" suppressHydrationWarning>
                {platformItems.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    {item.subItems ? (
                      isClient ? (
                        <Collapsible
                          open={
                            collapsibleStates[item.title] ??
                            isPathActive(item.url, pathname)
                          }
                          onOpenChange={(isOpen) =>
                            updateCollapsibleState(item.title, isOpen)
                          }
                        >
                          <CollapsibleTrigger asChild>
                            <SidebarMenuButton
                              className={`group w-full justify-between rounded-xl px-4 py-3 transition-all duration-200 hover:scale-[1.02] hover:bg-accent/80 active:scale-[0.98] data-[state=open]:bg-accent/60 ${
                                isPathActive(item.url, pathname)
                                  ? "bg-accent/80 text-foreground shadow-sm"
                                  : ""
                              }`}
                              size="sm"
                            >
                              <div className="flex items-center gap-4">
                                <item.icon
                                  className={`h-5 w-5 ${
                                    isPathActive(item.url, pathname)
                                      ? "text-foreground"
                                      : "text-foreground/80"
                                  }`}
                                />
                                <span
                                  className={`font-semibold text-sm ${
                                    isPathActive(item.url, pathname)
                                      ? "text-foreground"
                                      : "text-foreground"
                                  }`}
                                >
                                  {item.title}
                                </span>
                                {item.badge && (
                                  <Badge
                                    variant={
                                      item.badge === t("badges.new")
                                        ? "default"
                                        : "secondary"
                                    }
                                    className="ml-auto h-5 px-2.5 py-0.5 font-bold text-[10px]"
                                  >
                                    {item.badge === t("badges.new") && (
                                      <Zap className="mr-1 h-2.5 w-2.5" />
                                    )}
                                    {item.badge}
                                  </Badge>
                                )}
                              </div>
                              <ChevronRight className="h-4 w-4 text-foreground/60 transition-all duration-300 group-data-[state=open]:rotate-90 group-data-[state=open]:text-foreground" />
                            </SidebarMenuButton>
                          </CollapsibleTrigger>
                          <CollapsibleContent className="transition-all duration-300 ease-out">
                            <SidebarMenuSub className="mt-3 ml-6 space-y-1 border-border/40 border-l pl-4">
                              {item.subItems.map((subItem) => (
                                <SidebarMenuSubItem key={subItem.title}>
                                  <SidebarMenuSubButton asChild>
                                    <a
                                      href={subItem.url}
                                      className={`group flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm transition-all duration-200 hover:translate-x-1 hover:bg-accent/60 ${
                                        pathname === subItem.url
                                          ? "bg-accent/80 font-semibold text-foreground shadow-sm"
                                          : ""
                                      }`}
                                    >
                                      <subItem.icon
                                        className={`h-4 w-4 ${
                                          pathname === subItem.url
                                            ? "text-foreground"
                                            : "text-foreground/70"
                                        }`}
                                      />
                                      <span
                                        className={`${
                                          pathname === subItem.url
                                            ? "font-semibold text-foreground"
                                            : "font-medium text-foreground/80"
                                        }`}
                                      >
                                        {subItem.title}
                                      </span>
                                    </a>
                                  </SidebarMenuSubButton>
                                </SidebarMenuSubItem>
                              ))}
                            </SidebarMenuSub>
                          </CollapsibleContent>
                        </Collapsible>
                      ) : (
                        // Server-side fallback: render as expanded but without interactivity
                        <div>
                          <SidebarMenuButton
                            className="group w-full justify-between rounded-xl px-4 py-3 transition-all duration-200 hover:scale-[1.02] hover:bg-accent/80 active:scale-[0.98]"
                            size="sm"
                          >
                            <div className="flex items-center gap-4">
                              <item.icon className="h-5 w-5 text-foreground/80" />
                              <span className="font-semibold text-foreground text-sm">
                                {item.title}
                              </span>
                              {item.badge && (
                                <Badge
                                  variant={
                                    item.badge === t("badges.new")
                                      ? "default"
                                      : "secondary"
                                  }
                                  className="ml-auto h-5 px-2.5 py-0.5 font-bold text-[10px]"
                                >
                                  {item.badge === t("badges.new") && (
                                    <Zap className="mr-1 h-2.5 w-2.5" />
                                  )}
                                  {item.badge}
                                </Badge>
                              )}
                            </div>
                            <ChevronRight className="h-4 w-4 text-foreground/60" />
                          </SidebarMenuButton>
                        </div>
                      )
                    ) : (
                      <SidebarMenuButton
                        asChild
                        className={`group rounded-xl px-4 py-3 transition-all duration-200 hover:scale-[1.02] hover:bg-accent/80 active:scale-[0.98] ${
                          isPathActive(item.url, pathname)
                            ? "bg-accent/80 text-foreground shadow-sm"
                            : ""
                        }`}
                        size="sm"
                      >
                        <a href={item.url} className="flex items-center gap-4">
                          <item.icon
                            className={`h-5 w-5 ${
                              isPathActive(item.url, pathname)
                                ? "text-foreground"
                                : "text-foreground/80"
                            }`}
                          />
                          <span
                            className={`flex-1 font-semibold text-sm ${
                              isPathActive(item.url, pathname)
                                ? "text-foreground"
                                : "text-foreground"
                            }`}
                          >
                            {item.title}
                          </span>
                          {item.badge && (
                            <Badge
                              variant={
                                item.badge === t("badges.new")
                                  ? "default"
                                  : "secondary"
                              }
                              className="h-5 px-2.5 py-0.5 font-bold text-[10px]"
                            >
                              {item.badge === t("badges.new") && (
                                <Zap className="mr-1 h-2.5 w-2.5" />
                              )}
                              {item.badge}
                            </Badge>
                          )}
                        </a>
                      </SidebarMenuButton>
                    )}
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>

        <SidebarFooter className="border-border/40 border-t px-4 py-4">
          {currentUser ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button
                  type="button"
                  className="group flex w-full cursor-pointer items-center gap-3 rounded-xl p-3 transition-all duration-200 hover:bg-accent/50 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                >
                  <Avatar className="h-10 w-10 shadow-md ring-2 ring-background">
                    <AvatarImage
                      src={currentUser.image}
                      alt={currentUser.name || currentUser.email}
                    />
                    <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 font-semibold text-primary text-sm">
                      {getUserInitials(currentUser.name, currentUser.email)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex min-w-0 flex-1 flex-col items-start">
                    <p className="truncate font-semibold text-foreground text-sm">
                      {currentUser.name || t("user_menu.user_default")}
                    </p>
                    <p className="truncate text-muted-foreground text-xs">
                      {currentUser.email}
                    </p>
                  </div>
                  <ChevronDown className="h-4 w-4 text-muted-foreground transition-all duration-200 group-hover:text-foreground group-data-[state=open]:rotate-180" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                sideOffset={8}
                className="w-56 p-2"
              >
                <DropdownMenuLabel className="px-3 py-2">
                  <div className="flex flex-col">
                    <p className="font-semibold text-sm">
                      {currentUser.name || t("user_menu.user_default")}
                    </p>
                    <p className="text-muted-foreground text-xs">
                      {currentUser.email}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="cursor-pointer gap-3 px-3 py-2.5">
                  <UserCog className="h-4 w-4" />
                  <span>{t("user_menu.profile")}</span>
                </DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer gap-3 px-3 py-2.5">
                  <Settings className="h-4 w-4" />
                  <span>{t("user_menu.preferences")}</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleSignOut}
                  className="cursor-pointer gap-3 px-3 py-2.5 text-red-600 focus:text-red-600"
                >
                  <LogOut className="h-4 w-4" />
                  <span>{t("user_menu.sign_out")}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <div className="flex items-center gap-3 rounded-xl bg-muted/30 p-3">
              <Avatar className="h-10 w-10 shadow-md ring-2 ring-background">
                <AvatarFallback className="bg-gradient-to-br from-muted to-muted/50 text-muted-foreground">
                  <User className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              <div className="flex min-w-0 flex-1 flex-col">
                <div className="mb-1 h-4 animate-pulse rounded bg-muted"></div>
                <div className="h-3 w-3/4 animate-pulse rounded bg-muted/60"></div>
              </div>
            </div>
          )}
        </SidebarFooter>

        <SidebarRail />
      </Sidebar>

      <SidebarInset className="flex flex-col">
        {/* Fixed Header with Breadcrumb */}
        <header className="sticky top-0 z-50 flex h-16 shrink-0 items-center gap-2 border-border/40 border-b bg-background/95 px-4 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <SidebarTrigger className="-ml-1 transition-colors duration-200 hover:bg-accent/50" />
          <div className="h-4 w-px bg-border/60" />
          <div className="flex flex-1 items-center justify-between">
            <SmartBreadcrumb items={breadcrumbItems} className="text-sm" />
          </div>
        </header>

        {/* Scrollable Content Area */}
        <div className="flex-1 overflow-auto">
          <div className="flex flex-1 flex-col gap-4 bg-background/50 p-6">
            {children}
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
