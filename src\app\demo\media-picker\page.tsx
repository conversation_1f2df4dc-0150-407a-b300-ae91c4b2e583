"use client";

import { useState } from "react";
import { MediaPicker, useMediaPicker } from "@/components/ui/media-picker";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Image as ImageIcon, FileText, Video, Music } from "lucide-react";

interface MediaFile {
  _id: string;
  originalName: string;
  contentType: string;
  storageId: string;
  category: "image" | "video" | "document" | "audio" | "other";
  size: number;
  url: string | null;
  _creationTime: number;
}

export default function MediaPickerDemo() {
  const [selectedFiles, setSelectedFiles] = useState<MediaFile[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [pickerMode, setPickerMode] = useState<"single" | "multiple">("single");

  const handleFileSelect = (file: MediaFile) => {
    if (pickerMode === "single") {
      setSelectedFiles([file]);
    } else {
      setSelectedFiles(prev => [...prev, file]);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / k ** i).toFixed(1))} ${sizes[i]}`;
  };

  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith("image/")) return <ImageIcon className="h-4 w-4" />;
    if (contentType.startsWith("video/")) return <Video className="h-4 w-4" />;
    if (contentType.startsWith("audio/")) return <Music className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            响应式媒体选择器演示
          </h1>
          <p className="text-gray-600">
            全新设计的媒体选择器，完美适配所有设备尺寸
          </p>
        </div>

        {/* Controls */}
        <Card>
          <CardHeader>
            <CardTitle>控制面板</CardTitle>
            <CardDescription>
              选择不同的模式来测试媒体选择器
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={pickerMode === "single" ? "default" : "outline"}
                onClick={() => setPickerMode("single")}
                size="sm"
              >
                单选模式
              </Button>
              <Button
                variant={pickerMode === "multiple" ? "default" : "outline"}
                onClick={() => setPickerMode("multiple")}
                size="sm"
              >
                多选模式
              </Button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={() => setIsOpen(true)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                打开媒体选择器
              </Button>
              <Button
                variant="outline"
                onClick={() => setSelectedFiles([])}
              >
                清空选择
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Selected Files Display */}
        <Card>
          <CardHeader>
            <CardTitle>已选择的文件</CardTitle>
            <CardDescription>
              {selectedFiles.length === 0 
                ? "还没有选择任何文件" 
                : `已选择 ${selectedFiles.length} 个文件`
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {selectedFiles.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                <p>点击上方按钮选择文件</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {selectedFiles.map((file) => (
                  <div
                    key={file._id}
                    className="border rounded-lg p-3 space-y-2"
                  >
                    <div className="flex items-center gap-2">
                      {getFileIcon(file.contentType)}
                      <span className="font-medium text-sm truncate flex-1">
                        {file.originalName}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{formatFileSize(file.size)}</span>
                      <Badge variant="secondary" className="text-xs">
                        {file.category}
                      </Badge>
                    </div>
                    {file.url && file.category === "image" && (
                      <div className="aspect-video bg-gray-100 rounded overflow-hidden">
                        <img
                          src={file.url}
                          alt={file.originalName}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Features */}
        <Card>
          <CardHeader>
            <CardTitle>新特性</CardTitle>
            <CardDescription>
              这个全新的媒体选择器包含以下改进
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-green-700">✅ 响应式设计</h4>
                <p className="text-sm text-gray-600">
                  完美适配手机、平板和桌面设备
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-green-700">✅ 图片预览</h4>
                <p className="text-sm text-gray-600">
                  点击图片可全屏预览，支持缩放
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-green-700">✅ 多种视图</h4>
                <p className="text-sm text-gray-600">
                  网格视图和列表视图，适应不同需求
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-green-700">✅ 智能搜索</h4>
                <p className="text-sm text-gray-600">
                  实时搜索文件名，快速找到目标文件
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-green-700">✅ 分类筛选</h4>
                <p className="text-sm text-gray-600">
                  按文件类型筛选，提高选择效率
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-green-700">✅ 无障碍访问</h4>
                <p className="text-sm text-gray-600">
                  支持键盘导航和屏幕阅读器
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Media Picker */}
      <MediaPicker
        open={isOpen}
        onOpenChange={setIsOpen}
        onSelect={handleFileSelect}
        multiple={pickerMode === "multiple"}
        allowedTypes={["all"]}
      />
    </div>
  );
}
