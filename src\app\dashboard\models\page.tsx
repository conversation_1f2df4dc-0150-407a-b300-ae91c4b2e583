export default function ModelsPage() {
  return (
    <div className="space-y-6">
      <div className="grid gap-4">
        <div className="rounded-lg border bg-card p-6 text-card-foreground shadow-sm">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="font-semibold text-lg">Available Models</h3>
            <button
              type="button"
              className="rounded-md bg-primary px-4 py-2 text-primary-foreground text-sm hover:bg-primary/90"
            >
              Create Model
            </button>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between rounded-lg border p-4">
              <div>
                <h4 className="font-medium">User Model</h4>
                <p className="text-muted-foreground text-sm">
                  Manages user data and authentication
                </p>
              </div>
              <div className="flex items-center gap-2">
                <span className="rounded-full bg-green-100 px-2 py-1 text-green-800 text-xs">
                  Active
                </span>
                <button
                  type="button"
                  className="rounded-md border px-3 py-1 text-sm hover:bg-accent"
                >
                  Edit
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between rounded-lg border p-4">
              <div>
                <h4 className="font-medium">Product Model</h4>
                <p className="text-muted-foreground text-sm">
                  Product catalog and inventory management
                </p>
              </div>
              <div className="flex items-center gap-2">
                <span className="rounded-full bg-green-100 px-2 py-1 text-green-800 text-xs">
                  Active
                </span>
                <button
                  type="button"
                  className="rounded-md border px-3 py-1 text-sm hover:bg-accent"
                >
                  Edit
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between rounded-lg border p-4">
              <div>
                <h4 className="font-medium">Order Model</h4>
                <p className="text-muted-foreground text-sm">
                  Order processing and tracking
                </p>
              </div>
              <div className="flex items-center gap-2">
                <span className="rounded-full bg-yellow-100 px-2 py-1 text-xs text-yellow-800">
                  Draft
                </span>
                <button
                  type="button"
                  className="rounded-md border px-3 py-1 text-sm hover:bg-accent"
                >
                  Edit
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
