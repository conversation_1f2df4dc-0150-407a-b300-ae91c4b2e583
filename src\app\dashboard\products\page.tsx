"use client";

import { api } from "@convex/api";
import { useQuery } from "convex/react";
import {
  Building2,
  Clock,
  Edit,
  Eye,
  Grid3X3,
  List,
  MoreHorizontal,
  Plus,
  Search,
  SlidersHorizontal,
  Star,
  Tag,
  Trash2,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";

type ViewMode = "grid" | "list";

export default function ProductsPage() {
  const t = useTranslations("products");
  const locale = useLocale();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedLanguage, setSelectedLanguage] = useState<string>(locale);
  const [viewMode, setViewMode] = useState<ViewMode>("grid");

  // Fetch products and categories
  const products = useQuery(api.products.getAllProducts, {
    language: selectedLanguage,
    limit: 50,
  });

  const categories = useQuery(api.productCategories.getActiveCategories, {
    language: selectedLanguage,
  });

  // Filter products based on search term and filters
  const filteredProducts =
    products?.filter((product) => {
      // Category filter
      if (
        selectedCategory !== "all" &&
        product.categoryId !== selectedCategory
      ) {
        return false;
      }

      // Status filter
      if (selectedStatus !== "all" && product.status !== selectedStatus) {
        return false;
      }

      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const productName =
          product.name[selectedLanguage] ||
          Object.values(product.name)[0] ||
          "";
        const productDescription =
          product.description[selectedLanguage] ||
          Object.values(product.description)[0] ||
          "";

        return (
          productName.toLowerCase().includes(searchLower) ||
          productDescription.toLowerCase().includes(searchLower)
        );
      }

      return true;
    }) || [];

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "published":
        return "bg-green-50 text-green-700 border-green-200 hover:bg-green-100";
      case "draft":
        return "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100";
      case "discontinued":
        return "bg-red-50 text-red-700 border-red-200 hover:bg-red-100";
      default:
        return "bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100";
    }
  };

  const formatDate = (timestamp?: number) => {
    if (!timestamp) return t("status.unpublished");
    return new Date(timestamp).toLocaleDateString(
      selectedLanguage === "zh" ? "zh-CN" : "en-US",
      {
        year: "numeric",
        month: "short",
        day: "numeric",
      },
    );
  };

  const formatRelativeTime = (timestamp?: number) => {
    if (!timestamp) return "";
    const now = new Date();
    const date = new Date(timestamp);
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return t("time.hours_ago", { hours: Math.floor(diffInHours) });
    } else if (diffInHours < 24 * 7) {
      return t("time.days_ago", { days: Math.floor(diffInHours / 24) });
    } else {
      return formatDate(timestamp);
    }
  };

  // Cover Image Component
  const CoverImage = ({
    coverImageId,
    name,
    className = "",
  }: {
    coverImageId?: string;
    name: string;
    className?: string;
  }) => {
    const coverImageInfo = useQuery(
      api.media.getMediaFileByStorageId,
      coverImageId ? { storageId: coverImageId } : "skip",
    );

    if (!coverImageId || !coverImageInfo?.url) {
      return (
        <div
          className={`flex items-center justify-center bg-gray-100 ${className}`}
        >
          <Building2 className="h-8 w-8 text-gray-400" />
        </div>
      );
    }

    return (
      <div className={`relative overflow-hidden ${className}`}>
        <Image
          src={coverImageInfo.url}
          alt={name}
          fill
          className="object-cover transition-transform duration-200 group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>
    );
  };

  // Statistics
  const totalProducts = filteredProducts.length;
  const publishedCount = filteredProducts.filter(
    (p) => p.status === "published",
  ).length;
  const draftCount = filteredProducts.filter(
    (p) => p.status === "draft",
  ).length;
  const featuredCount = filteredProducts.filter((p) => p.isFeatured).length;

  return (
    <div className="min-h-screen bg-gray-50/50">
      <div className="mx-auto max-w-7xl space-y-8 p-6">
        {/* Header */}
        <div className="flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between">
          <div className="space-y-1">
            <h1 className="font-bold text-3xl text-gray-900 tracking-tight">
              {t("title")}
            </h1>
            <p className="text-gray-600">{t("description")}</p>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1 rounded-lg border bg-white p-1">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="h-8 px-3"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="h-8 px-3"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
            <Link href="/dashboard/products/create">
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="mr-2 h-4 w-4" />
                {t("actions.create")}
              </Button>
            </Link>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <Card className="border-0 bg-white shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="rounded-full bg-blue-50 p-3">
                  <Building2 className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="font-medium text-gray-600 text-sm">
                    {t("stats.total_products")}
                  </p>
                  <p className="font-bold text-2xl text-gray-900">
                    {totalProducts}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-white shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="rounded-full bg-green-50 p-3">
                  <Eye className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="font-medium text-gray-600 text-sm">
                    {t("stats.published")}
                  </p>
                  <p className="font-bold text-2xl text-gray-900">
                    {publishedCount}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-white shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="rounded-full bg-amber-50 p-3">
                  <Edit className="h-6 w-6 text-amber-600" />
                </div>
                <div className="ml-4">
                  <p className="font-medium text-gray-600 text-sm">
                    {t("stats.drafts")}
                  </p>
                  <p className="font-bold text-2xl text-gray-900">
                    {draftCount}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-white shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="rounded-full bg-purple-50 p-3">
                  <Star className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="font-medium text-gray-600 text-sm">
                    {t("stats.featured")}
                  </p>
                  <p className="font-bold text-2xl text-gray-900">
                    {featuredCount}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="border-0 bg-white shadow-sm">
          <CardContent className="p-6">
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-2">
                <SlidersHorizontal className="h-4 w-4 text-gray-500" />
                <span className="font-medium text-gray-700 text-sm">
                  {t("filters.title")}
                </span>
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
                {/* Search */}
                <div className="relative lg:col-span-2">
                  <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder={t("filters.search_placeholder")}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="border-gray-200 pl-10 focus:border-blue-400 focus:ring-blue-400"
                  />
                </div>

                {/* Category Filter */}
                <Select
                  value={selectedCategory}
                  onValueChange={setSelectedCategory}
                >
                  <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
                    <SelectValue placeholder={t("filters.category")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      {t("filters.all_categories")}
                    </SelectItem>
                    {categories?.map((category) => (
                      <SelectItem key={category._id} value={category._id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Status Filter */}
                <Select
                  value={selectedStatus}
                  onValueChange={setSelectedStatus}
                >
                  <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
                    <SelectValue placeholder={t("filters.status")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      {t("filters.all_statuses")}
                    </SelectItem>
                    <SelectItem value="published">
                      {t("status.published")}
                    </SelectItem>
                    <SelectItem value="draft">{t("status.draft")}</SelectItem>
                    <SelectItem value="discontinued">
                      {t("status.discontinued")}
                    </SelectItem>
                  </SelectContent>
                </Select>

                {/* Language Filter */}
                <Select
                  value={selectedLanguage}
                  onValueChange={setSelectedLanguage}
                >
                  <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
                    <SelectValue placeholder={t("filters.language")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">{t("languages.en")}</SelectItem>
                    <SelectItem value="zh">{t("languages.zh")}</SelectItem>
                    <SelectItem value="ja">{t("languages.ja")}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Products Section */}
        {filteredProducts.length === 0 ? (
          <Card className="border-0 bg-white shadow-sm">
            <CardContent className="flex flex-col items-center justify-center py-16">
              <div className="mb-6 rounded-full bg-gray-100 p-6">
                <Building2 className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="mb-2 font-semibold text-gray-900 text-xl">
                {t("empty.title")}
              </h3>
              <p className="mb-6 max-w-md text-center text-gray-600">
                {t("empty.description")}
              </p>
              <Link href="/dashboard/products/create">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="mr-2 h-4 w-4" />
                  {t("actions.create_first")}
                </Button>
              </Link>
            </CardContent>
          </Card>
        ) : viewMode === "grid" ? (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredProducts.map((product) => {
              const productName =
                product.name[selectedLanguage] ||
                Object.values(product.name)[0] ||
                "";
              const productDescription =
                product.description[selectedLanguage] ||
                Object.values(product.description)[0] ||
                "";

              return (
                <Card
                  key={product._id}
                  className="group cursor-pointer border-0 bg-white shadow-sm transition-all duration-200 hover:scale-[1.02] hover:shadow-md"
                >
                  <CardContent className="p-0">
                    {/* Cover Image */}
                    <CoverImage
                      coverImageId={product.coverImageId}
                      name={productName}
                      className="h-48 w-full rounded-t-lg"
                    />

                    {/* Header */}
                    <div className="p-6 pb-4">
                      <div className="mb-3 flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          <Badge
                            className={`${getStatusColor(product.status)} transition-colors`}
                          >
                            {t(`status.${product.status || "draft"}`)}
                          </Badge>
                          {product.isFeatured && (
                            <Badge className="border-purple-200 bg-purple-50 text-purple-700">
                              <Star className="mr-1 h-3 w-3" />
                              {t("badges.featured")}
                            </Badge>
                          )}
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 opacity-0 transition-opacity group-hover:opacity-100"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            <DropdownMenuLabel>
                              {t("actions.title")}
                            </DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem asChild>
                              <Link href={`/dashboard/products/${product._id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                {t("actions.view")}
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link
                                href={`/dashboard/products/${product._id}/edit`}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                {t("actions.edit")}
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600 focus:text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              {t("actions.delete")}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <h3 className="mb-2 line-clamp-2 font-semibold text-gray-900 text-lg transition-colors group-hover:text-blue-600">
                        {productName}
                      </h3>

                      <p className="mb-4 line-clamp-3 text-gray-600 text-sm">
                        {productDescription}
                      </p>
                    </div>

                    <Separator />

                    {/* Footer */}
                    <div className="p-6 pt-4">
                      <div className="flex items-center justify-between text-gray-500 text-xs">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-1">
                            <Tag className="h-3 w-3" />
                            <span className="truncate">
                              {product.category.name[selectedLanguage] || Object.values(product.category.name)[0] || ""}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>
                            {formatRelativeTime(
                              product.publishedAt || product._creationTime,
                            )}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        ) : (
          <Card className="border-0 bg-white shadow-sm">
            <CardContent className="p-0">
              {filteredProducts.map((product, index) => {
                const productName =
                  product.name[selectedLanguage] ||
                  Object.values(product.name)[0] ||
                  "";
                const productDescription =
                  product.description[selectedLanguage] ||
                  Object.values(product.description)[0] ||
                  "";

                return (
                  <div key={product._id}>
                    <div className="group p-6 transition-colors hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex min-w-0 flex-1 items-start gap-4">
                          {/* Cover Image Thumbnail */}
                          <CoverImage
                            coverImageId={product.coverImageId}
                            name={productName}
                            className="h-16 w-16 shrink-0 rounded-lg"
                          />

                          <div className="min-w-0 flex-1">
                            <div className="mb-2 flex items-center gap-3">
                              <h3 className="truncate font-semibold text-gray-900 text-lg transition-colors group-hover:text-blue-600">
                                {productName}
                              </h3>
                              <Badge
                                className={`${getStatusColor(product.status)} transition-colors`}
                              >
                                {t(`status.${product.status || "draft"}`)}
                              </Badge>
                              {product.isFeatured && (
                                <Badge className="border-purple-200 bg-purple-50 text-purple-700">
                                  <Star className="mr-1 h-3 w-3" />
                                  {t("badges.featured")}
                                </Badge>
                              )}
                            </div>

                            <div className="mb-3 flex items-center gap-6 text-gray-500 text-sm">
                              <div className="flex items-center gap-1">
                                <Tag className="h-4 w-4" />
                                <span>{product.category.name[selectedLanguage] || Object.values(product.category.name)[0] || ""}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                <span>
                                  {formatRelativeTime(
                                    product.publishedAt ||
                                      product._creationTime,
                                  )}
                                </span>
                              </div>
                            </div>

                            <p className="line-clamp-2 text-gray-600 text-sm">
                              {productDescription}
                            </p>
                          </div>
                        </div>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="ml-4">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            <DropdownMenuLabel>
                              {t("actions.title")}
                            </DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem asChild>
                              <Link href={`/dashboard/products/${product._id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                {t("actions.view")}
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link
                                href={`/dashboard/products/${product._id}/edit`}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                {t("actions.edit")}
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600 focus:text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              {t("actions.delete")}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                    {index < filteredProducts.length - 1 && <Separator />}
                  </div>
                );
              })}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
