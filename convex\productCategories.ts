import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// 定义多语言文本类型
const multiLanguageText = v.record(v.string(), v.string());
const multiLanguageArray = v.record(v.string(), v.array(v.string()));

/**
 * 创建产品分类
 */
export const createCategory = mutation({
  args: {
    name: multiLanguageText,
    description: multiLanguageText,
    sortOrder: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
    seoKeywords: v.optional(multiLanguageArray),
    seoDescription: v.optional(multiLanguageText),
    slug: v.optional(multiLanguageText),
  },
  returns: v.id("products_categories"),
  handler: async (ctx, args) => {
    return await ctx.db.insert("products_categories", {
      ...args,
      isActive: args.isActive ?? true,
    });
  },
});

/**
 * 更新产品分类
 */
export const updateCategory = mutation({
  args: {
    categoryId: v.id("products_categories"),
    name: v.optional(multiLanguageText),
    description: v.optional(multiLanguageText),
    sortOrder: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
    seoKeywords: v.optional(multiLanguageArray),
    seoDescription: v.optional(multiLanguageText),
    slug: v.optional(multiLanguageText),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { categoryId, ...updates } = args;
    const category = await ctx.db.get(categoryId);
    if (!category) {
      throw new Error("Category not found");
    }

    await ctx.db.patch(categoryId, updates);
    return null;
  },
});

/**
 * 删除产品分类
 */
export const deleteCategory = mutation({
  args: {
    categoryId: v.id("products_categories"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const category = await ctx.db.get(args.categoryId);
    if (!category) {
      throw new Error("Category not found");
    }

    // 检查是否有产品使用此分类
    const productsInCategory = await ctx.db
      .query("products")
      .withIndex("by_category", (q) => q.eq("categoryId", args.categoryId))
      .first();

    if (productsInCategory) {
      throw new Error("Cannot delete category with existing products");
    }

    await ctx.db.delete(args.categoryId);
    return null;
  },
});

/**
 * 获取分类详情
 */
export const getCategory = query({
  args: {
    categoryId: v.id("products_categories"),
    language: v.optional(v.string()),
  },
  returns: v.union(
    v.object({
      _id: v.id("products_categories"),
      _creationTime: v.number(),
      name: v.string(),
      description: v.string(),
      sortOrder: v.optional(v.number()),
      isActive: v.optional(v.boolean()),
      seoKeywords: v.optional(v.array(v.string())),
      seoDescription: v.optional(v.string()),
      slug: v.optional(v.string()),
      productCount: v.number(),
    }),
    v.null(),
  ),
  handler: async (ctx, args) => {
    const language = args.language || "en";
    const category = await ctx.db.get(args.categoryId);
    if (!category) {
      return null;
    }

    // 统计该分类下的产品数量
    const products = await ctx.db
      .query("products")
      .withIndex("by_category", (q) => q.eq("categoryId", args.categoryId))
      .collect();

    return {
      _id: category._id,
      _creationTime: category._creationTime,
      name:
        category.name[language] ||
        category.name[Object.keys(category.name)[0]] ||
        "",
      description:
        category.description[language] ||
        category.description[Object.keys(category.description)[0]] ||
        "",
      sortOrder: category.sortOrder,
      isActive: category.isActive,
      seoKeywords: category.seoKeywords?.[language] || [],
      seoDescription: category.seoDescription?.[language],
      slug: category.slug?.[language],
      productCount: products.length,
    };
  },
});

/**
 * 获取所有分类列表（包含完整多语言数据）
 */
export const getCategoriesWithFullData = query({
  args: {
    includeInactive: v.optional(v.boolean()),
  },
  returns: v.array(
    v.object({
      _id: v.id("products_categories"),
      _creationTime: v.number(),
      name: multiLanguageText,
      description: multiLanguageText,
      sortOrder: v.optional(v.number()),
      isActive: v.optional(v.boolean()),
      seoKeywords: v.optional(multiLanguageArray),
      seoDescription: v.optional(multiLanguageText),
      slug: v.optional(multiLanguageText),
      productCount: v.number(),
    }),
  ),
  handler: async (ctx, args) => {
    let categories = await ctx.db
      .query("products_categories")
      .withIndex("by_sort_order")
      .collect();

    // 如果不包含非活跃分类，则过滤掉
    if (!args.includeInactive) {
      categories = categories.filter((category) => category.isActive !== false);
    }

    // 统计每个分类下的产品数量
    const categoriesWithCount = await Promise.all(
      categories.map(async (category) => {
        const products = await ctx.db
          .query("products")
          .withIndex("by_category", (q) => q.eq("categoryId", category._id))
          .collect();

        return {
          ...category,
          productCount: products.length,
        };
      }),
    );

    // 按排序顺序排列，如果没有排序顺序则按创建时间排列
    return categoriesWithCount.sort((a, b) => {
      const orderA = a.sortOrder ?? a._creationTime;
      const orderB = b.sortOrder ?? b._creationTime;
      return orderA - orderB;
    });
  },
});

/**
 * 获取所有分类列表
 */
export const getCategories = query({
  args: {
    includeInactive: v.optional(v.boolean()),
    language: v.optional(v.string()),
  },
  returns: v.array(
    v.object({
      _id: v.id("products_categories"),
      _creationTime: v.number(),
      name: v.string(),
      description: v.string(),
      sortOrder: v.optional(v.number()),
      isActive: v.optional(v.boolean()),
      seoKeywords: v.optional(v.array(v.string())),
      seoDescription: v.optional(v.string()),
      slug: v.optional(v.string()),
      productCount: v.number(),
    }),
  ),
  handler: async (ctx, args) => {
    const language = args.language || "en";

    let categories = await ctx.db
      .query("products_categories")
      .withIndex("by_sort_order")
      .collect();

    // 如果不包含非活跃分类，则过滤掉
    if (!args.includeInactive) {
      categories = categories.filter((category) => category.isActive !== false);
    }

    // 统计每个分类下的产品数量
    const categoriesWithCount = await Promise.all(
      categories.map(async (category) => {
        const products = await ctx.db
          .query("products")
          .withIndex("by_category", (q) => q.eq("categoryId", category._id))
          .collect();

        return {
          _id: category._id,
          _creationTime: category._creationTime,
          name:
            category.name[language] ||
            category.name[Object.keys(category.name)[0]] ||
            "",
          description:
            category.description[language] ||
            category.description[Object.keys(category.description)[0]] ||
            "",
          sortOrder: category.sortOrder,
          isActive: category.isActive,
          seoKeywords: category.seoKeywords?.[language] || [],
          seoDescription: category.seoDescription?.[language],
          slug: category.slug?.[language],
          productCount: products.length,
        };
      }),
    );

    // 按排序顺序排列，如果没有排序顺序则按创建时间排列
    return categoriesWithCount.sort((a, b) => {
      const orderA = a.sortOrder ?? a._creationTime;
      const orderB = b.sortOrder ?? b._creationTime;
      return orderA - orderB;
    });
  },
});

/**
 * 获取活跃分类列表（用于前端选择器）
 */
export const getActiveCategories = query({
  args: {
    language: v.optional(v.string()),
  },
  returns: v.array(
    v.object({
      _id: v.id("products_categories"),
      name: v.string(),
      description: v.string(),
      sortOrder: v.optional(v.number()),
    }),
  ),
  handler: async (ctx, args) => {
    const language = args.language || "en";

    const categories = await ctx.db
      .query("products_categories")
      .withIndex("by_sort_order")
      .filter((q) => q.neq(q.field("isActive"), false))
      .collect();

    return categories
      .sort((a, b) => {
        const orderA = a.sortOrder ?? a._creationTime;
        const orderB = b.sortOrder ?? b._creationTime;
        return orderA - orderB;
      })
      .map((category) => ({
        _id: category._id,
        name:
          category.name[language] ||
          category.name[Object.keys(category.name)[0]] ||
          "",
        description:
          category.description[language] ||
          category.description[Object.keys(category.description)[0]] ||
          "",
        sortOrder: category.sortOrder,
      }));
  },
});

/**
 * 启用/禁用分类
 */
export const toggleCategoryStatus = mutation({
  args: {
    categoryId: v.id("products_categories"),
    isActive: v.boolean(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const category = await ctx.db.get(args.categoryId);
    if (!category) {
      throw new Error("Category not found");
    }

    await ctx.db.patch(args.categoryId, {
      isActive: args.isActive,
    });
    return null;
  },
});
