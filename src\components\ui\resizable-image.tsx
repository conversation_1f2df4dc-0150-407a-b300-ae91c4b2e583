"use client";

import { useCallback, useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";

interface ResizableImageProps {
  src: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  onResize?: (width: number, height: number) => void;
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
  maintainAspectRatio?: boolean;
}

export function ResizableImage({
  src,
  alt,
  className,
  style,
  onResize,
  minWidth = 50,
  minHeight = 50,
  maxWidth = 1000,
  maxHeight = 1000,
  maintainAspectRatio = false,
}: ResizableImageProps) {
  const [isResizing, setIsResizing] = useState(false);
  const [isSelected, setIsSelected] = useState(false);
  const [currentWidth, setCurrentWidth] = useState(
    (style?.width as number) || 300,
  );
  const [currentHeight, setCurrentHeight] = useState(
    (style?.height as number) || 200,
  );
  const [aspectRatio, setAspectRatio] = useState(1.5);

  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const isResizingRef = useRef(false);
  const resizeDataRef = useRef<{
    handle: string;
    startX: number;
    startY: number;
    startWidth: number;
    startHeight: number;
  } | null>(null);

  // Initialize image dimensions
  useEffect(() => {
    if (imageRef.current) {
      const img = imageRef.current;
      const onLoad = () => {
        const naturalWidth = img.naturalWidth;
        const naturalHeight = img.naturalHeight;
        const ratio = naturalWidth / naturalHeight;
        setAspectRatio(ratio);

        // Use style dimensions if provided, otherwise calculate from natural dimensions
        const styleWidth = style?.width as number;
        const styleHeight = style?.height as number;

        if (styleWidth && styleHeight) {
          setCurrentWidth(styleWidth);
          setCurrentHeight(styleHeight);
        } else {
          // Set initial size (limit to reasonable range)
          let width = Math.min(naturalWidth, 500);
          let height = width / ratio;

          if (height > 400) {
            height = 400;
            width = height * ratio;
          }

          setCurrentWidth(width);
          setCurrentHeight(height);
          onResize?.(width, height);
        }
      };

      if (img.complete) {
        onLoad();
      } else {
        img.addEventListener("load", onLoad);
        return () => img.removeEventListener("load", onLoad);
      }
    }
  }, [style?.width, style?.height, onResize]);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizingRef.current || !resizeDataRef.current) return;

      const { handle, startX, startY, startWidth, startHeight } =
        resizeDataRef.current;
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      let newWidth = startWidth;
      let newHeight = startHeight;

      switch (handle) {
        case "se": // 右下角
          newWidth = startWidth + deltaX;
          newHeight = maintainAspectRatio
            ? newWidth / aspectRatio
            : startHeight + deltaY;
          break;
        case "sw": // 左下角
          newWidth = startWidth - deltaX;
          newHeight = maintainAspectRatio
            ? newWidth / aspectRatio
            : startHeight + deltaY;
          break;
        case "ne": // 右上角
          newWidth = startWidth + deltaX;
          newHeight = maintainAspectRatio
            ? newWidth / aspectRatio
            : startHeight - deltaY;
          break;
        case "nw": // 左上角
          newWidth = startWidth - deltaX;
          newHeight = maintainAspectRatio
            ? newWidth / aspectRatio
            : startHeight - deltaY;
          break;
        case "n": // 上边
          newHeight = startHeight - deltaY;
          newWidth = maintainAspectRatio ? newHeight * aspectRatio : startWidth;
          break;
        case "s": // 下边
          newHeight = startHeight + deltaY;
          newWidth = maintainAspectRatio ? newHeight * aspectRatio : startWidth;
          break;
        case "e": // 右边
          newWidth = startWidth + deltaX;
          newHeight = maintainAspectRatio
            ? newWidth / aspectRatio
            : startHeight;
          break;
        case "w": // 左边
          newWidth = startWidth - deltaX;
          newHeight = maintainAspectRatio
            ? newWidth / aspectRatio
            : startHeight;
          break;
      }

      // Apply size constraints
      newWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));
      newHeight = Math.max(minHeight, Math.min(maxHeight, newHeight));

      setCurrentWidth(newWidth);
      setCurrentHeight(newHeight);
      onResize?.(newWidth, newHeight);
    },
    [
      aspectRatio,
      maintainAspectRatio,
      minWidth,
      minHeight,
      maxWidth,
      maxHeight,
      onResize,
    ],
  );

  const handleMouseUp = useCallback(() => {
    isResizingRef.current = false;
    resizeDataRef.current = null;
    setIsResizing(false);

    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);
  }, [handleMouseMove]);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent, handle: string) => {
      e.preventDefault();
      e.stopPropagation();

      isResizingRef.current = true;
      setIsResizing(true);

      resizeDataRef.current = {
        handle,
        startX: e.clientX,
        startY: e.clientY,
        startWidth: currentWidth,
        startHeight: currentHeight,
      };

      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    },
    [currentWidth, currentHeight, handleMouseMove, handleMouseUp],
  );

  const handleImageClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsSelected(true);
  };

  // Click outside to deselect
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(e.target as Node)
      ) {
        setIsSelected(false);
      }
    };

    if (isSelected) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isSelected]);

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative inline-block",
        isSelected && "ring-2 ring-blue-500 ring-offset-0",
        className,
      )}
      style={{
        width: currentWidth,
        height: currentHeight,
        margin: 0,
        padding: 0,
        ...style,
      }}
    >
      {/* biome-ignore lint/a11y/useKeyWithClickEvents: Image click is intentional for selection */}
      <img
        ref={imageRef}
        src={src}
        alt={alt}
        className="h-full w-full cursor-pointer rounded object-cover"
        onClick={handleImageClick}
        draggable={false}
        style={{
          userSelect: "none",
          pointerEvents: isResizing ? "none" : "auto",
          margin: 0,
          padding: 0,
          display: "block",
        }}
      />

      {isSelected && (
        <>
          {/* Corner handles */}
          <div
            className="absolute h-4 w-4 cursor-nw-resize rounded-full border-2 border-white bg-blue-500 shadow-sm hover:bg-blue-600"
            style={{ top: -8, left: -8 }}
            onMouseDown={(e) => handleMouseDown(e, "nw")}
            role="button"
            tabIndex={-1}
            aria-label="调整左上角"
          />
          <div
            className="absolute h-4 w-4 cursor-ne-resize rounded-full border-2 border-white bg-blue-500 shadow-sm hover:bg-blue-600"
            style={{ top: -8, right: -8 }}
            onMouseDown={(e) => handleMouseDown(e, "ne")}
            role="button"
            tabIndex={-1}
            aria-label="调整右上角"
          />
          <div
            className="absolute h-4 w-4 cursor-sw-resize rounded-full border-2 border-white bg-blue-500 shadow-sm hover:bg-blue-600"
            style={{ bottom: -8, left: -8 }}
            onMouseDown={(e) => handleMouseDown(e, "sw")}
            role="button"
            tabIndex={-1}
            aria-label="调整左下角"
          />
          <div
            className="absolute h-4 w-4 cursor-se-resize rounded-full border-2 border-white bg-blue-500 shadow-sm hover:bg-blue-600"
            style={{ bottom: -8, right: -8 }}
            onMouseDown={(e) => handleMouseDown(e, "se")}
            role="button"
            tabIndex={-1}
            aria-label="调整右下角"
          />

          {/* Edge handles */}
          <div
            className="absolute h-4 w-4 cursor-n-resize rounded-full border-2 border-white bg-blue-500 shadow-sm hover:bg-blue-600"
            style={{ top: -8, left: "50%", transform: "translateX(-50%)" }}
            onMouseDown={(e) => handleMouseDown(e, "n")}
            role="button"
            tabIndex={-1}
            aria-label="调整上边"
          />
          <div
            className="absolute h-4 w-4 cursor-s-resize rounded-full border-2 border-white bg-blue-500 shadow-sm hover:bg-blue-600"
            style={{ bottom: -8, left: "50%", transform: "translateX(-50%)" }}
            onMouseDown={(e) => handleMouseDown(e, "s")}
            role="button"
            tabIndex={-1}
            aria-label="调整下边"
          />
          <div
            className="absolute h-4 w-4 cursor-w-resize rounded-full border-2 border-white bg-blue-500 shadow-sm hover:bg-blue-600"
            style={{ left: -8, top: "50%", transform: "translateY(-50%)" }}
            onMouseDown={(e) => handleMouseDown(e, "w")}
            role="button"
            tabIndex={-1}
            aria-label="调整左边"
          />
          <div
            className="absolute h-4 w-4 cursor-e-resize rounded-full border-2 border-white bg-blue-500 shadow-sm hover:bg-blue-600"
            style={{ right: -8, top: "50%", transform: "translateY(-50%)" }}
            onMouseDown={(e) => handleMouseDown(e, "e")}
            role="button"
            tabIndex={-1}
            aria-label="调整右边"
          />
        </>
      )}
    </div>
  );
}
