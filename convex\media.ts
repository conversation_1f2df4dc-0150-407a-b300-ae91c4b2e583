import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

/**
 * 生成文件上传 URL
 */
export const generateUploadUrl = mutation({
  args: {},
  returns: v.string(),
  handler: async (ctx) => {
    // 这里可以添加权限检查
    return await ctx.storage.generateUploadUrl();
  },
});

/**
 * 保存文件信息到数据库
 */
export const saveFileInfo = mutation({
  args: {
    storageId: v.id("_storage"),
    originalName: v.string(),
    size: v.number(),
    contentType: v.string(),
    category: v.union(
      v.literal("image"),
      v.literal("video"),
      v.literal("document"),
      v.literal("audio"),
      v.literal("other"),
    ),
    tags: v.optional(v.array(v.string())),
    description: v.optional(v.string()),
    altText: v.optional(v.string()),
    isPublic: v.optional(v.boolean()),
    folderId: v.optional(v.id("media_folders")),
  },
  returns: v.id("media_files"),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    const userId = identity?.subject;

    // 确定文件分类
    let category = args.category;
    if (category === "other") {
      if (args.contentType.startsWith("image/")) {
        category = "image";
      } else if (args.contentType.startsWith("video/")) {
        category = "video";
      } else if (args.contentType.startsWith("audio/")) {
        category = "audio";
      } else if (
        args.contentType === "application/pdf" ||
        args.contentType.includes("document") ||
        args.contentType.includes("text/")
      ) {
        category = "document";
      }
    }

    const fileId = await ctx.db.insert("media_files", {
      storageId: args.storageId,
      originalName: args.originalName,
      size: args.size,
      contentType: args.contentType,
      category,
      tags: args.tags,
      description: args.description,
      altText: args.altText,
      isPublic: args.isPublic ?? true,
      uploadedBy: userId,
      accessCount: 0,
    });

    // 如果指定了文件夹，创建关联
    if (args.folderId) {
      await ctx.db.insert("media_file_folders", {
        fileId,
        folderId: args.folderId!,
      });
    }

    return fileId;
  },
});

/**
 * 获取媒体文件列表
 */
export const getMediaFiles = query({
  args: {
    category: v.optional(
      v.union(
        v.literal("image"),
        v.literal("video"),
        v.literal("document"),
        v.literal("audio"),
        v.literal("other"),
        v.literal("all"),
      ),
    ),
    folderId: v.optional(v.id("media_folders")),
    limit: v.optional(v.number()),
    searchQuery: v.optional(v.string()),
  },
  returns: v.array(
    v.object({
      _id: v.id("media_files"),
      _creationTime: v.number(),
      originalName: v.string(),
      size: v.number(),
      contentType: v.string(),
      storageId: v.id("_storage"),
      category: v.union(
        v.literal("image"),
        v.literal("video"),
        v.literal("document"),
        v.literal("audio"),
        v.literal("other"),
      ),
      tags: v.optional(v.array(v.string())),
      description: v.optional(v.string()),
      altText: v.optional(v.string()),
      isPublic: v.optional(v.boolean()),
      uploadedBy: v.optional(v.string()),
      accessCount: v.optional(v.number()),
      url: v.union(v.string(), v.null()),
    }),
  ),
  handler: async (ctx, args) => {
    let files: any[];

    // 按分类筛选
    if (args.category && args.category !== "all") {
      files = await ctx.db
        .query("media_files")
        .withIndex("by_category", (q) => q.eq("category", args.category as any))
        .order("desc")
        .take(args.limit ?? 50);
    } else {
      files = await ctx.db
        .query("media_files")
        .order("desc")
        .take(args.limit ?? 50);
    }

    // 搜索过滤
    if (args.searchQuery) {
      const searchLower = args.searchQuery.toLowerCase();
      files = files.filter(
        (file) =>
          file.originalName.toLowerCase().includes(searchLower) ||
          file.description?.toLowerCase().includes(searchLower) ||
          file.tags?.some((tag: string) =>
            tag.toLowerCase().includes(searchLower),
          ),
      );
    }

    // 文件夹过滤
    if (args.folderId) {
      const folderFiles = await ctx.db
        .query("media_file_folders")
        .withIndex("by_folder", (q) => q.eq("folderId", args.folderId!))
        .collect();
      const fileIds = new Set(folderFiles.map((f) => f.fileId));
      files = files.filter((file) => fileIds.has(file._id));
    }

    // 获取文件 URL
    const filesWithUrls = await Promise.all(
      files.map(async (file) => {
        const url = await ctx.storage.getUrl(file.storageId);
        return {
          ...file,
          url,
        };
      }),
    );

    return filesWithUrls;
  },
});

/**
 * 获取单个媒体文件信息
 */
export const getMediaFile = query({
  args: {
    fileId: v.id("media_files"),
  },
  returns: v.union(
    v.object({
      _id: v.id("media_files"),
      _creationTime: v.number(),
      originalName: v.string(),
      size: v.number(),
      contentType: v.string(),
      storageId: v.id("_storage"),
      category: v.union(
        v.literal("image"),
        v.literal("video"),
        v.literal("document"),
        v.literal("audio"),
        v.literal("other"),
      ),
      tags: v.optional(v.array(v.string())),
      description: v.optional(v.string()),
      altText: v.optional(v.string()),
      isPublic: v.optional(v.boolean()),
      uploadedBy: v.optional(v.string()),
      accessCount: v.optional(v.number()),
      url: v.union(v.string(), v.null()),
    }),
    v.null(),
  ),
  handler: async (ctx, args) => {
    const file = await ctx.db.get(args.fileId);
    if (!file) return null;

    const url = await ctx.storage.getUrl(file.storageId);

    return {
      ...file,
      url,
    };
  },
});

/**
 * 根据存储ID获取媒体文件信息
 */
export const getMediaFileByStorageId = query({
  args: {
    storageId: v.id("_storage"),
  },
  returns: v.union(
    v.object({
      _id: v.id("media_files"),
      _creationTime: v.number(),
      originalName: v.string(),
      size: v.number(),
      contentType: v.string(),
      storageId: v.id("_storage"),
      category: v.union(
        v.literal("image"),
        v.literal("video"),
        v.literal("document"),
        v.literal("audio"),
        v.literal("other"),
      ),
      tags: v.optional(v.array(v.string())),
      description: v.optional(v.string()),
      altText: v.optional(v.string()),
      isPublic: v.optional(v.boolean()),
      uploadedBy: v.optional(v.string()),
      accessCount: v.optional(v.number()),
      url: v.union(v.string(), v.null()),
    }),
    v.null(),
  ),
  handler: async (ctx, args) => {
    const file = await ctx.db
      .query("media_files")
      .withIndex("by_storage_id", (q) => q.eq("storageId", args.storageId))
      .first();

    if (!file) return null;

    const url = await ctx.storage.getUrl(file.storageId);

    return {
      ...file,
      url,
    };
  },
});

/**
 * 根据存储URL查找媒体文件信息
 */
export const getMediaFileByUrl = query({
  args: {
    url: v.string(),
  },
  returns: v.union(
    v.object({
      _id: v.id("media_files"),
      _creationTime: v.number(),
      originalName: v.string(),
      size: v.number(),
      contentType: v.string(),
      storageId: v.id("_storage"),
      category: v.union(
        v.literal("image"),
        v.literal("video"),
        v.literal("document"),
        v.literal("audio"),
        v.literal("other"),
      ),
      tags: v.optional(v.array(v.string())),
      description: v.optional(v.string()),
      altText: v.optional(v.string()),
      isPublic: v.optional(v.boolean()),
      uploadedBy: v.optional(v.string()),
      accessCount: v.optional(v.number()),
      url: v.union(v.string(), v.null()),
    }),
    v.null(),
  ),
  handler: async (ctx, args) => {
    // 从所有媒体文件中查找匹配的URL
    const files = await ctx.db.query("media_files").collect();

    for (const file of files) {
      const fileUrl = await ctx.storage.getUrl(file.storageId);
      if (fileUrl === args.url) {
        return {
          ...file,
          url: fileUrl,
        };
      }
    }

    return null;
  },
});

/**
 * 更新媒体文件信息
 */
export const updateMediaFile = mutation({
  args: {
    fileId: v.id("media_files"),
    originalName: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    description: v.optional(v.string()),
    altText: v.optional(v.string()),
    isPublic: v.optional(v.boolean()),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const file = await ctx.db.get(args.fileId);
    if (!file) {
      throw new Error("File not found");
    }

    const updates: any = {};
    if (args.originalName !== undefined)
      updates.originalName = args.originalName;
    if (args.tags !== undefined) updates.tags = args.tags;
    if (args.description !== undefined) updates.description = args.description;
    if (args.altText !== undefined) updates.altText = args.altText;
    if (args.isPublic !== undefined) updates.isPublic = args.isPublic;

    await ctx.db.patch(args.fileId, updates);
    return null;
  },
});

/**
 * 删除媒体文件
 */
export const deleteMediaFile = mutation({
  args: {
    fileId: v.id("media_files"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const file = await ctx.db.get(args.fileId);
    if (!file) {
      throw new Error("File not found");
    }

    // 删除存储中的文件
    await ctx.storage.delete(file.storageId);

    // 删除文件夹关联
    const folderAssociations = await ctx.db
      .query("media_file_folders")
      .withIndex("by_file", (q) => q.eq("fileId", args.fileId))
      .collect();

    for (const association of folderAssociations) {
      await ctx.db.delete(association._id);
    }

    // 删除文件记录
    await ctx.db.delete(args.fileId);

    return null;
  },
});

/**
 * 记录文件访问
 */
export const recordFileAccess = mutation({
  args: {
    fileId: v.id("media_files"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const file = await ctx.db.get(args.fileId);
    if (!file) return null;

    await ctx.db.patch(args.fileId, {
      accessCount: (file.accessCount ?? 0) + 1,
      lastAccessedAt: Date.now(),
    });

    return null;
  },
});

// 文件夹管理 API

/**
 * 创建文件夹
 */
export const createFolder = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    parentFolderId: v.optional(v.id("media_folders")),
    color: v.optional(v.string()),
  },
  returns: v.id("media_folders"),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    const userId = identity?.subject;

    return await ctx.db.insert("media_folders", {
      name: args.name,
      description: args.description,
      parentFolderId: args.parentFolderId,
      color: args.color,
      createdBy: userId,
      sortOrder: Date.now(),
    });
  },
});

/**
 * 获取文件夹列表
 */
export const getFolders = query({
  args: {
    parentFolderId: v.optional(v.id("media_folders")),
  },
  returns: v.array(
    v.object({
      _id: v.id("media_folders"),
      _creationTime: v.number(),
      name: v.string(),
      description: v.optional(v.string()),
      parentFolderId: v.optional(v.id("media_folders")),
      color: v.optional(v.string()),
      sortOrder: v.optional(v.number()),
      createdBy: v.optional(v.string()),
      fileCount: v.number(),
    }),
  ),
  handler: async (ctx, args) => {
    const folders = await ctx.db
      .query("media_folders")
      .withIndex("by_parent", (q) =>
        q.eq("parentFolderId", args.parentFolderId),
      )
      .order("asc")
      .collect();

    // 获取每个文件夹的文件数量
    const foldersWithCount = await Promise.all(
      folders.map(async (folder) => {
        const fileCount = await ctx.db
          .query("media_file_folders")
          .withIndex("by_folder", (q) => q.eq("folderId", folder._id))
          .collect();

        return {
          ...folder,
          fileCount: fileCount.length,
        };
      }),
    );

    return foldersWithCount;
  },
});

/**
 * 更新文件夹
 */
export const updateFolder = mutation({
  args: {
    folderId: v.id("media_folders"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    color: v.optional(v.string()),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const folder = await ctx.db.get(args.folderId);
    if (!folder) {
      throw new Error("Folder not found");
    }

    const updates: any = {};
    if (args.name !== undefined) updates.name = args.name;
    if (args.description !== undefined) updates.description = args.description;
    if (args.color !== undefined) updates.color = args.color;

    await ctx.db.patch(args.folderId, updates);
    return null;
  },
});

/**
 * 删除文件夹
 */
export const deleteFolder = mutation({
  args: {
    folderId: v.id("media_folders"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // 检查文件夹是否有子文件夹
    const childFolders = await ctx.db
      .query("media_folders")
      .withIndex("by_parent", (q) => q.eq("parentFolderId", args.folderId))
      .first();

    if (childFolders) {
      throw new Error("Cannot delete folder with subfolders");
    }

    // 删除文件关联
    const fileAssociations = await ctx.db
      .query("media_file_folders")
      .withIndex("by_folder", (q) => q.eq("folderId", args.folderId))
      .collect();

    for (const association of fileAssociations) {
      await ctx.db.delete(association._id);
    }

    // 删除文件夹
    await ctx.db.delete(args.folderId);

    return null;
  },
});

/**
 * 移动文件到文件夹
 */
export const moveFileToFolder = mutation({
  args: {
    fileId: v.id("media_files"),
    folderId: v.optional(v.id("media_folders")),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // 删除现有的文件夹关联
    const existingAssociations = await ctx.db
      .query("media_file_folders")
      .withIndex("by_file", (q) => q.eq("fileId", args.fileId))
      .collect();

    for (const association of existingAssociations) {
      await ctx.db.delete(association._id);
    }

    // 如果有新的文件夹，创建关联
    if (args.folderId) {
      await ctx.db.insert("media_file_folders", {
        fileId: args.fileId,
        folderId: args.folderId,
      });
    }

    return null;
  },
});

/**
 * 获取文件统计信息
 */
export const getMediaStats = query({
  args: {},
  returns: v.object({
    totalFiles: v.number(),
    totalSize: v.number(),
    categoryStats: v.object({
      image: v.number(),
      video: v.number(),
      document: v.number(),
      audio: v.number(),
      other: v.number(),
    }),
  }),
  handler: async (ctx) => {
    const files = await ctx.db.query("media_files").collect();

    const stats = {
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      categoryStats: {
        image: 0,
        video: 0,
        document: 0,
        audio: 0,
        other: 0,
      },
    };

    files.forEach((file) => {
      stats.categoryStats[file.category]++;
    });

    return stats;
  },
});
