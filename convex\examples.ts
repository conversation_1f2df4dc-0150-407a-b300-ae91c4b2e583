import { v } from "convex/values";
import type { Id } from "./_generated/dataModel";
import { mutation } from "./_generated/server";

// 定义多语言文本类型
const _multiLanguageText = v.record(v.string(), v.string()); // Record<languageCode, text>

// 定义多语言数组类型
const _multiLanguageArray = v.record(v.string(), v.array(v.string())); // Record<languageCode, string[]>

/**
 * 创建对夹式法兰球阀产品示例
 */
export const createValveProductExample = mutation({
  args: {},
  returns: v.object({
    productId: v.id("products"),
    standardIds: v.array(v.id("product_standards")),
  }),
  handler: async (ctx) => {
    // 1. 首先创建或获取产品分类
    const existingCategories = await ctx.db
      .query("products_categories")
      .filter((q) => q.neq(q.field("isActive"), false))
      .collect();

    // 查找是否已存在"自动阀"分类
    const category = existingCategories.find(
      (cat) => cat.name.zh === "自动阀" || cat.name.en === "Automatic Valve",
    );

    let categoryId: Id<"products_categories">;
    if (!category) {
      categoryId = await ctx.db.insert("products_categories", {
        name: {
          zh: "自动阀",
          en: "Automatic Valve",
        },
        description: {
          zh: "工业自动化阀门产品",
          en: "Industrial automation valve products",
        },
        sortOrder: 1,
        isActive: true,
        seoKeywords: {
          zh: ["自动阀", "工业阀门", "自动化设备"],
          en: ["automatic valve", "industrial valve", "automation equipment"],
        },
        seoDescription: {
          zh: "专业的工业自动化阀门产品系列",
          en: "Professional industrial automation valve product series",
        },
        slug: {
          zh: "zi-dong-fa",
          en: "automatic-valve",
        },
      });
    } else {
      categoryId = category._id;
    }

    // 2. 创建产品
    const productId = await ctx.db.insert("products", {
      name: {
        zh: "对夹式法兰球阀",
        en: "Wafer Flange Ball Valve",
      },
      title: {
        zh: "对夹式法兰球阀",
        en: "Wafer Flange Ball Valve",
      },
      description: {
        zh: "该压板式球阀结构：由阀体、阀盖结合球芯组装而成，球芯利用阀杆连接，轴线旋转90度可实现开启跟关闭的作用。主要用于管道开关，紧急切断，也可设计球芯V型开口达到流量调节功能。",
        en: "This pressure plate ball valve structure: consists of valve body, valve cover combined with ball core assembly, the ball core is connected by valve stem, and the axis rotation of 90 degrees can achieve opening and closing functions. Mainly used for pipeline switching, emergency shut-off, and can also design ball core V-shaped opening to achieve flow control function.",
      },
      categoryId: categoryId,
      status: "published",
      isFeatured: true,
      publishedAt: Date.now(),
      sortOrder: 1,
      originalLanguage: "zh",
      availableLanguages: ["zh", "en"],
      seoKeywords: {
        zh: ["对夹式", "法兰球阀", "自动阀", "管道开关", "紧急切断"],
        en: [
          "wafer",
          "flange ball valve",
          "automatic valve",
          "pipeline switch",
          "emergency cutoff",
        ],
      },
      seoDescription: {
        zh: "对夹式法兰球阀，适用于管道开关和紧急切断，可选V型开口实现流量调节功能",
        en: "Wafer flange ball valve, suitable for pipeline switching and emergency cutoff, optional V-shaped opening for flow control function",
      },
      slug: {
        zh: "dui-jia-shi-fa-lan-qiu-fa",
        en: "wafer-flange-ball-valve",
      },
    });

    // 3. 添加产品标准
    const standards = [
      {
        standardType: {
          zh: "设计标准",
          en: "Design Standard",
        },
        standardValues: {
          zh: ["ANSI B16.34", "API608"],
          en: ["ANSI B16.34", "API608"],
        },
        sortOrder: 1,
        originalLanguage: "zh",
        availableLanguages: ["zh", "en"],
      },
      {
        standardType: {
          zh: "壁厚标准",
          en: "Wall Thickness Standard",
        },
        standardValues: {
          zh: ["ANSI B16.34", "EN12516-3"],
          en: ["ANSI B16.34", "EN12516-3"],
        },
        sortOrder: 2,
        originalLanguage: "zh",
        availableLanguages: ["zh", "en"],
      },
      {
        standardType: {
          zh: "螺纹标准",
          en: "Thread Standard",
        },
        standardValues: {
          zh: [
            "ANSI B1.20.1",
            "BS21",
            "DIN 2999/259",
            "ISO 228/1",
            "ISO7/1",
            "JIS B0203",
          ],
          en: [
            "ANSI B1.20.1",
            "BS21",
            "DIN 2999/259",
            "ISO 228/1",
            "ISO7/1",
            "JIS B0203",
          ],
        },
        sortOrder: 3,
        originalLanguage: "zh",
        availableLanguages: ["zh", "en"],
      },
      {
        standardType: {
          zh: "承插焊标准",
          en: "Socket Weld Standard",
        },
        standardValues: {
          zh: ["ASME B16.11"],
          en: ["ASME B16.11"],
        },
        sortOrder: 4,
        originalLanguage: "zh",
        availableLanguages: ["zh", "en"],
      },
      {
        standardType: {
          zh: "对焊端标准",
          en: "Butt Weld End Standard",
        },
        standardValues: {
          zh: ["ASME B16.25/ISO1127/EN12627"],
          en: ["ASME B16.25/ISO1127/EN12627"],
        },
        sortOrder: 5,
        originalLanguage: "zh",
        availableLanguages: ["zh", "en"],
      },
      {
        standardType: {
          zh: "法兰端标准",
          en: "Flange End Standard",
        },
        standardValues: {
          zh: [
            "ASME B16.5 CLASS 150/300",
            "EN1092-1",
            "HG20592 GB/T9113",
            "PN10-PN40",
            "JIS B2238 10K/20K",
          ],
          en: [
            "ASME B16.5 CLASS 150/300",
            "EN1092-1",
            "HG20592 GB/T9113",
            "PN10-PN40",
            "JIS B2238 10K/20K",
          ],
        },
        sortOrder: 6,
        originalLanguage: "zh",
        availableLanguages: ["zh", "en"],
      },
      {
        standardType: {
          zh: "检测标准",
          en: "Test Standard",
        },
        standardValues: {
          zh: ["API 598", "EN 12266"],
          en: ["API 598", "EN 12266"],
        },
        sortOrder: 7,
        originalLanguage: "zh",
        availableLanguages: ["zh", "en"],
      },
    ];

    const standardIds: Id<"product_standards">[] = [];
    for (const standard of standards) {
      const standardId = await ctx.db.insert("product_standards", {
        productId,
        standardType: standard.standardType,
        standardValues: standard.standardValues,
        sortOrder: standard.sortOrder,
        originalLanguage: standard.originalLanguage,
        availableLanguages: standard.availableLanguages,
        isActive: true,
      });
      standardIds.push(standardId);
    }

    return {
      productId,
      standardIds,
    };
  },
});
