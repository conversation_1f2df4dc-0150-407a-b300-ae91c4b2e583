# AGENT.md

No need to run `bun run build` or `bun run dev` to build and run the app on any changes.

## Commands
- `bun run dev` - Start development server with Turbopack
- `bun run build` - Build production version
- `bun run lint` - Run Biome linter and formatter
- `bun run lint:fix` - Auto-fix linting issues and format code
- **No test framework configured**

## Architecture
- **Framework**: Next.js 15 with App Router and Turbopack
- **Database**: Convex for real-time data with schema in `convex/schema.ts`
- **Auth**: Convex Auth with OAuth providers (Google, GitHub)
- **UI**: Radix UI components with Tailwind CSS
- **i18n**: next-intl for internationalization
- **Styling**: Tailwind CSS with class-variance-authority
- **State**: React hooks with Convex real-time subscriptions

## Code Style (from biome.json & Cursor rules)
- **Imports**: Use `@/` for src imports, `@convex/` for generated Convex files
- **Formatting**: 2-space indentation, double quotes, organize imports on save
- **Tailwind**: Use `cn()` utility for class merging, auto-sort classes
- **Types**: Strict TypeScript, use Convex validators and generated types
- **Convex**: Follow new function syntax with args/returns validators
- **Components**: Use Radix UI primitives, consistent naming conventions
- **Error handling**: Throw descriptive errors, validate inputs
