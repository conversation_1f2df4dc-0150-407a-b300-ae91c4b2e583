"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Search, Plus, MoreHorizontal, Edit, Trash2 } from "lucide-react";

const users = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    status: "Active",
    joinDate: "2024-01-15",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    joinDate: "2024-02-20",
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "Inactive",
    joinDate: "2024-01-10",
  },
  {
    id: 4,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Moderator",
    status: "Active",
    joinDate: "2024-03-05",
  },
];

export default function UsersPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="font-bold text-2xl tracking-tight">User Management</h2>
          <p className="text-muted-foreground">
            Manage your users and their permissions.
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add User
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative max-w-sm flex-1">
          <Search className="absolute top-2.5 left-2 h-4 w-4 text-muted-foreground" />
          <Input placeholder="Search users..." className="pl-8" />
        </div>
      </div>

      <div className="rounded-md border">
        <div className="p-4">
          <div className="mb-4 grid grid-cols-5 gap-4 font-medium text-muted-foreground text-sm">
            <div>Name</div>
            <div>Email</div>
            <div>Role</div>
            <div>Status</div>
            <div>Actions</div>
          </div>

          <div className="space-y-3">
            {users.map((user) => (
              <div
                key={user.id}
                className="grid grid-cols-5 items-center gap-4 border-t py-3"
              >
                <div>
                  <div className="font-medium">{user.name}</div>
                  <div className="text-muted-foreground text-sm">
                    Joined {user.joinDate}
                  </div>
                </div>
                <div className="text-sm">{user.email}</div>
                <div>
                  <Badge
                    variant={user.role === "Admin" ? "default" : "secondary"}
                  >
                    {user.role}
                  </Badge>
                </div>
                <div>
                  <Badge
                    variant={user.status === "Active" ? "default" : "secondary"}
                    className={user.status === "Active" ? "bg-green-500" : ""}
                  >
                    {user.status}
                  </Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="border-t bg-muted/50 px-4 py-3">
          <div className="text-muted-foreground text-sm">
            Showing {users.length} of {users.length} users
          </div>
        </div>
      </div>
    </div>
  );
}
