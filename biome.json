{"$schema": "https://biomejs.dev/schemas/2.0.0-beta.5/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**", "!.next/**", "!node_modules/**", "!convex/_generated/**"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}, "linter": {"enabled": true, "rules": {"recommended": true, "nursery": {"useSortedClasses": {"level": "error", "fix": "safe", "options": {"attributes": ["className"], "functions": ["cn"]}}}}, "domains": {"next": "all"}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}