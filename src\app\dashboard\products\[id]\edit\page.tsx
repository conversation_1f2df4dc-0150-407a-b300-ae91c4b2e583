"use client";

import { api } from "@convex/api";
import { useMutation, useQuery } from "convex/react";
import {
  ArrowLeft,
  Building2,
  Check,
  Globe,
  ImageIcon,
  Plus,
  Save,
  Star,
  Upload,
  X,
} from "lucide-react";
import Link from "next/link";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";

const SUPPORTED_LANGUAGES = [
  { code: "zh", name: "中文" },
  { code: "en", name: "English" },
  { code: "ja", name: "日本語" },
];

type ProductFormData = {
  name: Record<string, string>;
  title: Record<string, string>;
  description: Record<string, string>;
  specifications: Record<string, string>;
  instructions: Record<string, string>;
  categoryId: string;
  status: "draft" | "published" | "discontinued";
  isFeatured: boolean;
  coverImageId?: string;
  seoKeywords: Record<string, string[]>;
  seoDescription: Record<string, string>;
  slug: Record<string, string>;
  availableLanguages: string[];
};

type ProductStandard = {
  _id?: string;
  type: Record<string, string>;
  values: Record<string, string[]>;
};

export default function EditProductPage() {
  const params = useParams();
  const t = useTranslations("products");
  const locale = useLocale();
  const router = useRouter();
  const [currentLanguage, setCurrentLanguage] = useState<string>(locale);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const productId = params.id as string;

  const [formData, setFormData] = useState<ProductFormData>({
    name: {},
    title: {},
    description: {},
    specifications: {},
    instructions: {},
    categoryId: "",
    status: "draft",
    isFeatured: false,
    seoKeywords: {},
    seoDescription: {},
    slug: {},
    availableLanguages: [locale],
  });

  const [standards, setStandards] = useState<ProductStandard[]>([]);

  // Queries
  const product = useQuery(api.products.getProduct, {
    productId: productId as any,
  });

  const categories = useQuery(api.productCategories.getActiveCategories, {
    language: currentLanguage,
  });

  // Mutations
  const updateProduct = useMutation(api.products.updateProduct);
  const batchSetStandards = useMutation(
    api.productStandards.batchSetProductStandards,
  );

  // Load product data when available
  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        title: product.title || {},
        description: product.description,
        specifications: product.specifications || {},
        instructions: product.instructions || {},
        categoryId: product.categoryId,
        status: product.status || "draft",
        isFeatured: product.isFeatured || false,
        coverImageId: product.coverImageId,
        seoKeywords: product.seoKeywords || {},
        seoDescription: product.seoDescription || {},
        slug: product.slug || {},
        availableLanguages: product.availableLanguages,
      });

      // Convert standards
      if (product.standards) {
        setStandards(
          product.standards.map((standard) => ({
            _id: standard._id,
            type: standard.standardType,
            values: standard.standardValues,
          })),
        );
      }

      // Set current language to first available or original
      const firstAvailable = product.availableLanguages[0] || locale;
      setCurrentLanguage(firstAvailable);
      setIsLoading(false);
    }
  }, [product, locale]);

  // Add language to available languages if not already present
  const addLanguage = (langCode: string) => {
    if (!formData.availableLanguages.includes(langCode)) {
      setFormData((prev) => ({
        ...prev,
        availableLanguages: [...prev.availableLanguages, langCode],
      }));
    }
    setCurrentLanguage(langCode);
  };

  // Remove language from available languages
  const removeLanguage = (langCode: string) => {
    if (langCode === product?.originalLanguage) {
      toast.error("无法删除原始语言");
      return;
    }
    setFormData((prev) => {
      const newData = { ...prev };
      newData.availableLanguages = prev.availableLanguages.filter(
        (lang) => lang !== langCode,
      );

      // Clean up language-specific fields
      const cleanField = (field: Record<string, string>) => {
        const cleaned = { ...field };
        delete cleaned[langCode];
        return cleaned;
      };

      newData.name = cleanField(prev.name);
      newData.title = cleanField(prev.title);
      newData.description = cleanField(prev.description);
      newData.specifications = cleanField(prev.specifications);
      newData.instructions = cleanField(prev.instructions);
      newData.seoDescription = cleanField(prev.seoDescription);
      newData.slug = cleanField(prev.slug);

      // Clean up seoKeywords
      const cleanKeywords = { ...prev.seoKeywords };
      delete cleanKeywords[langCode];
      newData.seoKeywords = cleanKeywords;

      return newData;
    });
    if (currentLanguage === langCode) {
      setCurrentLanguage(
        product?.originalLanguage || formData.availableLanguages[0],
      );
    }
  };

  // Update form field
  const updateField = (
    field: keyof ProductFormData,
    value: any,
    language?: string,
  ) => {
    if (
      language &&
      (field === "name" ||
        field === "title" ||
        field === "description" ||
        field === "specifications" ||
        field === "instructions" ||
        field === "seoDescription" ||
        field === "slug")
    ) {
      setFormData((prev) => ({
        ...prev,
        [field]: {
          ...prev[field],
          [language]: value,
        },
      }));
    } else if (language && field === "seoKeywords") {
      setFormData((prev) => ({
        ...prev,
        [field]: {
          ...prev[field],
          [language]: value
            .split(",")
            .map((k: string) => k.trim())
            .filter((k: string) => k),
        },
      }));
    } else {
      setFormData((prev) => ({ ...prev, [field]: value }));
    }
  };

  // Add new standard
  const addStandard = () => {
    setStandards((prev) => [...prev, { type: {}, values: {} }]);
  };

  // Update standard
  const updateStandard = (
    index: number,
    field: "type" | "values",
    language: string,
    value: string,
  ) => {
    setStandards((prev) =>
      prev.map((standard, i) => {
        if (i === index) {
          if (field === "values") {
            return {
              ...standard,
              [field]: {
                ...standard[field],
                [language]: value
                  .split(",")
                  .map((v) => v.trim())
                  .filter((v) => v),
              },
            };
          } else {
            return {
              ...standard,
              [field]: {
                ...standard[field],
                [language]: value,
              },
            };
          }
        }
        return standard;
      }),
    );
  };

  // Remove standard
  const removeStandard = (index: number) => {
    setStandards((prev) => prev.filter((_, i) => i !== index));
  };

  // Validate form
  const validateForm = () => {
    if (!formData.categoryId) {
      toast.error(t("shared.errors.select_category"));
      return false;
    }

    const hasName = Object.values(formData.name).some((name) => name?.trim());
    if (!hasName) {
      toast.error(t("shared.errors.fill_name"));
      return false;
    }

    const hasDescription = Object.values(formData.description).some((desc) =>
      desc?.trim(),
    );
    if (!hasDescription) {
      toast.error(t("shared.errors.fill_description"));
      return false;
    }

    return true;
  };

  // Submit form
  const handleSubmit = async (publish: boolean = false) => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      // Clean up form data
      const cleanFormData = {
        ...formData,
        status: publish ? ("published" as const) : formData.status,
      };

      // Update product
      await updateProduct({
        productId: productId as any,
        categoryId: cleanFormData.categoryId as any,
        name: cleanFormData.name,
        title: cleanFormData.title,
        description: cleanFormData.description,
        specifications: cleanFormData.specifications,
        instructions: cleanFormData.instructions,
        status: cleanFormData.status,
        isFeatured: cleanFormData.isFeatured,
        coverImageId: cleanFormData.coverImageId as any,
        seoKeywords: cleanFormData.seoKeywords,
        seoDescription: cleanFormData.seoDescription,
        slug: cleanFormData.slug,
        availableLanguages: cleanFormData.availableLanguages,
      });

      // Update standards
      const validStandards = standards
        .filter(
          (standard) =>
            Object.values(standard.type).some((type) => type?.trim()) &&
            Object.values(standard.values).some((values) => values?.length > 0),
        )
        .map((standard) => ({
          standardType: standard.type,
          standardValues: standard.values,
          originalLanguage: product?.originalLanguage || locale,
          availableLanguages: formData.availableLanguages,
        }));

      await batchSetStandards({
        productId: productId as any,
        standards: validStandards,
      });

      toast.success(
        publish
          ? t("shared.errors.published_success")
          : t("shared.errors.updated_success"),
      );
      router.push(`/dashboard/products/${productId}`);
    } catch (_error) {
      toast.error(t("shared.errors.save_failed_generic"));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calculate completion percentage
  const calculateCompletion = () => {
    let completed = 0;
    let total = 0;

    // Required fields
    total += 3; // name, description, category
    if (formData.name[currentLanguage]?.trim()) completed++;
    if (formData.description[currentLanguage]?.trim()) completed++;
    if (formData.categoryId) completed++;

    // Optional fields
    total += 4; // title, specifications, instructions, cover
    if (formData.title[currentLanguage]?.trim()) completed++;
    if (formData.specifications[currentLanguage]?.trim()) completed++;
    if (formData.instructions[currentLanguage]?.trim()) completed++;
    if (formData.coverImageId) completed++;

    return Math.round((completed / total) * 100);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50/50">
        <div className="mx-auto max-w-7xl p-6">
          <div className="mb-8 flex items-center gap-4">
            <Link href="/dashboard/products">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t("shared.ui.back_to_list")}
              </Button>
            </Link>
          </div>

          <Card className="p-8 text-center">
            <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
            <p>{t("shared.errors.loading_content")}</p>
          </Card>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50/50">
        <div className="mx-auto max-w-7xl p-6">
          <div className="mb-8 flex items-center gap-4">
            <Link href="/dashboard/products">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t("shared.ui.back_to_list")}
              </Button>
            </Link>
          </div>

          <Card className="p-8 text-center">
            <Building2 className="mx-auto mb-4 h-12 w-12 text-gray-400" />
            <h2 className="mb-2 font-semibold text-gray-900 text-xl">
              {t("shared.errors.product_not_found")}
            </h2>
            <p className="mb-6 text-gray-600">
              {t("shared.errors.product_not_found")}
            </p>
            <Link href="/dashboard/products">
              <Button>{t("shared.errors.back_to_products")}</Button>
            </Link>
          </Card>
        </div>
      </div>
    );
  }

  const completionPercentage = calculateCompletion();

  return (
    <div className="min-h-screen bg-gray-50/50">
      <div className="mx-auto max-w-7xl space-y-8 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href={`/dashboard/products/${productId}`}>
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t("shared.ui.back_to_detail")}
              </Button>
            </Link>
            <div>
              <h1 className="font-bold text-2xl text-gray-900">
                {t("edit.title")}
              </h1>
              <p className="text-gray-600 text-sm">{t("edit.description")}</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <span className="text-gray-600 text-sm">
                {t("shared.ui.completion_progress")}
              </span>
              <Badge
                variant={completionPercentage === 100 ? "default" : "secondary"}
              >
                {completionPercentage}%
              </Badge>
            </div>
            <Button
              variant="outline"
              onClick={() => handleSubmit(false)}
              disabled={isSubmitting}
            >
              <Save className="mr-2 h-4 w-4" />
              {t("shared.ui.save_changes")}
            </Button>
            <Button
              onClick={() => handleSubmit(true)}
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? (
                "保存中..."
              ) : (
                <>
                  <Check className="mr-2 h-4 w-4" />
                  {formData.status === "published"
                    ? t("shared.ui.save_changes")
                    : t("shared.ui.publish")}
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Main Content */}
          <div className="space-y-6 lg:col-span-2">
            {/* Language Management */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  {t("shared.ui.multilang_management")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  {formData.availableLanguages.map((langCode) => {
                    const lang = SUPPORTED_LANGUAGES.find(
                      (l) => l.code === langCode,
                    );
                    if (!lang) return null;

                    return (
                      <div key={langCode} className="flex items-center gap-1">
                        <Button
                          variant={
                            currentLanguage === langCode ? "default" : "outline"
                          }
                          size="sm"
                          onClick={() => setCurrentLanguage(langCode)}
                          className="h-8"
                        >
                          {lang.name}
                          {langCode === product.originalLanguage && (
                            <Badge
                              variant="secondary"
                              className="ml-1 h-4 px-1 text-xs"
                            >
                              {t("shared.ui.original_badge")}
                            </Badge>
                          )}
                        </Button>
                        {langCode !== product.originalLanguage && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeLanguage(langCode)}
                            className="h-6 w-6 p-0 text-red-500 hover:bg-red-50"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    );
                  })}

                  <Select onValueChange={addLanguage}>
                    <SelectTrigger className="h-8 w-32">
                      <Plus className="h-3 w-3" />
                      <SelectValue placeholder="添加语言" />
                    </SelectTrigger>
                    <SelectContent>
                      {SUPPORTED_LANGUAGES.filter(
                        (lang) =>
                          !formData.availableLanguages.includes(lang.code),
                      ).map((lang) => (
                        <SelectItem key={lang.code} value={lang.code}>
                          {lang.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="text-muted-foreground text-sm">
                  {t("shared.ui.currently_editing")}{" "}
                  <strong>
                    {
                      SUPPORTED_LANGUAGES.find(
                        (l) => l.code === currentLanguage,
                      )?.name
                    }
                  </strong>
                </div>
              </CardContent>
            </Card>

            {/* Product Content */}
            <Card>
              <CardHeader>
                <CardTitle>{t("shared.ui.product_content")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Product Name */}
                <div className="space-y-2">
                  <Label htmlFor="name" className="flex items-center gap-2">
                    {t("form.name")}
                    <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    placeholder={t("shared.ui.product_name_placeholder")}
                    value={formData.name[currentLanguage] || ""}
                    onChange={(e) =>
                      updateField("name", e.target.value, currentLanguage)
                    }
                  />
                </div>

                {/* Product Title */}
                <div className="space-y-2">
                  <Label htmlFor="title">{t("form.title")}</Label>
                  <Input
                    id="title"
                    placeholder={t("shared.ui.product_title_placeholder")}
                    value={formData.title[currentLanguage] || ""}
                    onChange={(e) =>
                      updateField("title", e.target.value, currentLanguage)
                    }
                  />
                </div>

                {/* Product Description */}
                <div className="space-y-2">
                  <Label
                    htmlFor="description"
                    className="flex items-center gap-2"
                  >
                    {t("form.description")}
                    <span className="text-red-500">*</span>
                  </Label>
                  <Textarea
                    id="description"
                    placeholder={t("shared.ui.product_description_placeholder")}
                    value={formData.description[currentLanguage] || ""}
                    onChange={(e) =>
                      updateField(
                        "description",
                        e.target.value,
                        currentLanguage,
                      )
                    }
                    rows={4}
                  />
                </div>

                {/* Product Specifications */}
                <div className="space-y-2">
                  <Label htmlFor="specifications">
                    {t("form.specifications")}
                  </Label>
                  <Textarea
                    id="specifications"
                    placeholder={t(
                      "shared.ui.product_specifications_placeholder",
                    )}
                    value={formData.specifications[currentLanguage] || ""}
                    onChange={(e) =>
                      updateField(
                        "specifications",
                        e.target.value,
                        currentLanguage,
                      )
                    }
                    rows={4}
                  />
                </div>

                {/* Instructions */}
                <div className="space-y-2">
                  <Label htmlFor="instructions">{t("form.instructions")}</Label>
                  <Textarea
                    id="instructions"
                    placeholder={t(
                      "shared.ui.product_instructions_placeholder",
                    )}
                    value={formData.instructions[currentLanguage] || ""}
                    onChange={(e) =>
                      updateField(
                        "instructions",
                        e.target.value,
                        currentLanguage,
                      )
                    }
                    rows={4}
                  />
                </div>

                {/* Cover Image */}
                <div className="space-y-2">
                  <Label>{t("form.cover_image")}</Label>
                  <div className="flex items-center justify-center rounded-lg border-2 border-gray-300 border-dashed px-6 py-4">
                    <div className="text-center">
                      <ImageIcon className="mx-auto h-8 w-8 text-gray-400" />
                      <div className="mt-2">
                        <Button variant="outline" size="sm">
                          <Upload className="mr-2 h-4 w-4" />
                          {formData.coverImageId
                            ? "更换封面图片"
                            : "上传封面图片"}
                        </Button>
                      </div>
                      <p className="mt-1 text-gray-500 text-xs">
                        PNG, JPG 最大 5MB
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Product Standards */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {t("standards.title")}
                  <Button onClick={addStandard} variant="outline" size="sm">
                    <Plus className="mr-2 h-4 w-4" />
                    {t("standards.add_standard")}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {standards.length === 0 ? (
                  <div className="py-8 text-center text-gray-500">
                    <Building2 className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                    <p>{t("standards.no_standards")}</p>
                    <p className="text-sm">
                      {t("standards.no_standards_description")}
                    </p>
                  </div>
                ) : (
                  standards.map((standard, index) => (
                    <div
                      key={`standard-${index}-${standard._id || 'new'}`}
                      className="space-y-3 rounded-lg bg-gray-50 p-4"
                    >
                      <div className="flex items-center justify-between">
                        <Label className="font-medium text-sm">
                          标准 {index + 1}
                        </Label>
                        <Button
                          onClick={() => removeStandard(index)}
                          variant="ghost"
                          size="sm"
                          className="text-red-500 hover:bg-red-50"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label className="text-sm">
                            {t("standards.standard_type")}
                          </Label>
                          <Input
                            placeholder={t("standards.type_placeholder")}
                            value={standard.type[currentLanguage] || ""}
                            onChange={(e) =>
                              updateStandard(
                                index,
                                "type",
                                currentLanguage,
                                e.target.value,
                              )
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm">
                            {t("standards.standard_values")}
                          </Label>
                          <Input
                            placeholder={t("standards.values_placeholder")}
                            value={
                              standard.values[currentLanguage]?.join(", ") || ""
                            }
                            onChange={(e) =>
                              updateStandard(
                                index,
                                "values",
                                currentLanguage,
                                e.target.value,
                              )
                            }
                          />
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </CardContent>
            </Card>

            {/* SEO Settings */}
            <Card>
              <CardHeader>
                <CardTitle>{t("shared.ui.seo_optimization")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="slug">{t("shared.ui.url_slug")}</Label>
                  <Input
                    id="slug"
                    placeholder={t("shared.ui.url_slug_placeholder")}
                    value={formData.slug[currentLanguage] || ""}
                    onChange={(e) =>
                      updateField("slug", e.target.value, currentLanguage)
                    }
                  />
                  <p className="text-muted-foreground text-xs">
                    {t("shared.ui.url_slug_help")}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="seoDescription">
                    {t("shared.ui.seo_description_label")}
                  </Label>
                  <Textarea
                    id="seoDescription"
                    placeholder={t("shared.ui.seo_description_placeholder")}
                    value={formData.seoDescription[currentLanguage] || ""}
                    onChange={(e) =>
                      updateField(
                        "seoDescription",
                        e.target.value,
                        currentLanguage,
                      )
                    }
                    rows={3}
                  />
                  <p className="text-muted-foreground text-xs">
                    {t("shared.ui.seo_description_help")}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="keywords">{t("shared.ui.keywords")}</Label>
                  <Input
                    id="keywords"
                    placeholder={t("shared.ui.keywords_placeholder")}
                    value={
                      formData.seoKeywords[currentLanguage]?.join(", ") || ""
                    }
                    onChange={(e) =>
                      updateField(
                        "seoKeywords",
                        e.target.value,
                        currentLanguage,
                      )
                    }
                  />
                  <p className="text-muted-foreground text-xs">
                    {t("shared.ui.keywords_help")}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Publish Settings */}
            <Card>
              <CardHeader>
                <CardTitle>{t("shared.ui.publish_settings")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>
                    {t("shared.ui.category")}{" "}
                    <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.categoryId}
                    onValueChange={(value) => updateField("categoryId", value)}
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={t("shared.ui.select_category")}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {categories?.map((category) => (
                        <SelectItem key={category._id} value={category._id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>{t("shared.ui.product_status")}</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(
                      value: "draft" | "published" | "discontinued",
                    ) => updateField("status", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">{t("status.draft")}</SelectItem>
                      <SelectItem value="published">
                        {t("status.published")}
                      </SelectItem>
                      <SelectItem value="discontinued">
                        {t("status.discontinued")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="featured"
                    checked={formData.isFeatured}
                    onCheckedChange={(checked) =>
                      updateField("isFeatured", checked)
                    }
                  />
                  <div className="space-y-1">
                    <Label
                      htmlFor="featured"
                      className="flex items-center gap-2"
                    >
                      <Star className="h-4 w-4" />
                      {t("shared.ui.featured_product")}
                    </Label>
                    <p className="text-muted-foreground text-xs">
                      {t("shared.ui.featured_description")}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Completion Status */}
            <Card>
              <CardHeader>
                <CardTitle>{t("shared.ui.completion_status")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{t("shared.ui.overall_progress")}</span>
                    <span>{completionPercentage}%</span>
                  </div>
                  <div className="h-2 overflow-hidden rounded-full bg-gray-200">
                    <div
                      className="h-full bg-blue-600 transition-all duration-300"
                      style={{ width: `${completionPercentage}%` }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
