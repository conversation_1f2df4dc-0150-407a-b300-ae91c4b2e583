"use client";

import { api } from "@convex/api";
import { useMutation, useQuery } from "convex/react";
import {
  Edit,
  Languages,
  MoreHorizontal,
  Plus,
  Tag,
  Trash2,
} from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { SUPPORTED_LANGUAGES } from "@/constants/languages";

export default function NewsCategoriesPage() {
  const t = useTranslations("news.categories");
  const locale = useLocale();
  const [viewLanguage, setViewLanguage] = useState<string>(locale);
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<any>(null);

  // Form states - 现在支持所有语言
  const [formData, setFormData] = useState({
    name: {} as Record<string, string>,
    description: {} as Record<string, string>,
    isActive: true,
  });

  // Queries and mutations
  const categories = useQuery(api.news.getNewsCategories, {
    includeInactive: true,
    language: viewLanguage,
  });

  // 获取编辑分类的完整多语言数据
  const editingCategoryData = useQuery(
    api.news.getNewsCategoryWithAllLanguages,
    editingCategory ? { categoryId: editingCategory._id } : "skip",
  );

  const createCategory = useMutation(api.news.createNewsCategory);
  const updateCategory = useMutation(api.news.updateNewsCategory);
  const deleteCategory = useMutation(api.news.deleteNewsCategory);

  const resetForm = () => {
    setFormData({
      name: {},
      description: {},
      isActive: true,
    });
    setEditingCategory(null);
  };

  const validateForm = () => {
    // 至少有一个语言的名称不能为空
    const hasAnyName = SUPPORTED_LANGUAGES.some((lang) =>
      formData.name[lang.code]?.trim(),
    );

    if (!hasAnyName) {
      alert(t("messages.name_required"));
      return false;
    }

    return true;
  };

  const handleCreate = async () => {
    if (!validateForm()) return;

    try {
      await createCategory({
        name: formData.name,
        description: formData.description,
        isActive: formData.isActive,
      });

      setIsCreateOpen(false);
      resetForm();
    } catch (error) {
      console.error("创建失败:", error);
      alert(t("messages.create_failed"));
    }
  };

  const handleUpdate = async () => {
    if (!editingCategory || !validateForm()) return;

    try {
      await updateCategory({
        categoryId: editingCategory._id,
        name: formData.name,
        description: formData.description,
        isActive: formData.isActive,
      });

      setEditingCategory(null);
      resetForm();
    } catch (error) {
      console.error("更新失败:", error);
      alert(t("messages.update_failed"));
    }
  };

  const handleDelete = async (categoryId: string) => {
    if (!confirm(t("messages.delete_confirm"))) return;

    try {
      await deleteCategory({ categoryId: categoryId as any });
    } catch (error) {
      console.error("删除失败:", error);
      alert(t("messages.delete_failed"));
    }
  };

  const openEdit = (category: any) => {
    setEditingCategory(category);
  };

  // 当获取到完整的分类数据时，更新表单
  useEffect(() => {
    if (editingCategoryData) {
      setFormData({
        name: editingCategoryData.name || {},
        description: editingCategoryData.description || {},
        isActive: editingCategoryData.isActive !== false,
      });
    }
  }, [editingCategoryData]);

  const updateFormData = (
    field: "name" | "description",
    languageCode: string,
    value: string,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: { ...prev[field], [languageCode]: value },
    }));
  };

  const renderLanguageInputs = (type: "name" | "description") => {
    return (
      <Tabs defaultValue={locale} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          {SUPPORTED_LANGUAGES.map((lang) => (
            <TabsTrigger key={lang.code} value={lang.code}>
              {lang.nativeName}
            </TabsTrigger>
          ))}
        </TabsList>

        {SUPPORTED_LANGUAGES.map((lang) => (
          <TabsContent key={lang.code} value={lang.code} className="space-y-2">
            {type === "name" ? (
              <Input
                placeholder={t("form.name_placeholder", {
                  language: lang.nativeName,
                })}
                value={formData.name[lang.code] || ""}
                onChange={(e) =>
                  updateFormData("name", lang.code, e.target.value)
                }
              />
            ) : (
              <Textarea
                placeholder={t("form.description_placeholder", {
                  language: lang.nativeName,
                })}
                value={formData.description[lang.code] || ""}
                onChange={(e) =>
                  updateFormData("description", lang.code, e.target.value)
                }
                rows={3}
              />
            )}
          </TabsContent>
        ))}
      </Tabs>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50/50">
      <div className="mx-auto max-w-7xl space-y-8 p-6">
        {/* Header */}
        <div className="flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between">
          <div className="space-y-1">
            <h1 className="font-bold text-3xl text-gray-900 tracking-tight">
              {t("title")}
            </h1>
            <p className="text-gray-600">{t("description")}</p>
          </div>

          <div className="flex items-center gap-3">
            <Select value={viewLanguage} onValueChange={setViewLanguage}>
              <SelectTrigger className="w-40 border-gray-200 focus:border-blue-400 focus:ring-blue-400">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {SUPPORTED_LANGUAGES.map((lang) => (
                  <SelectItem key={lang.code} value={lang.code}>
                    {lang.nativeName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
              <DialogTrigger asChild>
                <Button
                  onClick={resetForm}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  {t("actions.create")}
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>{t("create_dialog.title")}</DialogTitle>
                  <DialogDescription>
                    {t("create_dialog.description")}
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                  <div>
                    <Label className="font-medium text-base">
                      {t("form.name")}
                    </Label>
                    <p className="mb-3 text-muted-foreground text-sm">
                      {t("form.name_help")}
                    </p>
                    {renderLanguageInputs("name")}
                  </div>

                  <div>
                    <Label className="font-medium text-base">
                      {t("form.description")}
                    </Label>
                    <p className="mb-3 text-muted-foreground text-sm">
                      {t("form.description_help")}
                    </p>
                    {renderLanguageInputs("description")}
                  </div>
                </div>

                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateOpen(false)}
                  >
                    {t("actions.cancel")}
                  </Button>
                  <Button onClick={handleCreate}>{t("actions.create")}</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Categories Table */}
        <Card className="border-0 bg-white shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Tag className="h-5 w-5" />
              <span>{t("table.title")}</span>
            </CardTitle>
            <CardDescription className="text-gray-600">
              {t("current_language")}:{" "}
              {
                SUPPORTED_LANGUAGES.find((l) => l.code === viewLanguage)
                  ?.nativeName
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {categories && categories.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("table.name")}</TableHead>
                    <TableHead>{t("table.description")}</TableHead>
                    <TableHead>{t("table.article_count")}</TableHead>
                    <TableHead>{t("table.status")}</TableHead>
                    <TableHead className="w-[100px]">
                      {t("table.actions")}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {categories.map((category) => (
                    <TableRow key={category._id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <span>{category.name}</span>
                          <Languages className="h-4 w-4 text-gray-500" />
                        </div>
                      </TableCell>
                      <TableCell className="text-gray-600">
                        {category.description || t("table.no_description")}
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">
                          {t("table.articles_count", {
                            count: category.articleCount,
                          })}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            category.isActive !== false
                              ? "default"
                              : "secondary"
                          }
                          className={
                            category.isActive !== false
                              ? "border-green-200 bg-green-50 text-green-700 hover:bg-green-100"
                              : "border-gray-200 bg-gray-50 text-gray-600 hover:bg-gray-100"
                          }
                        >
                          {category.isActive !== false
                            ? t("table.enabled")
                            : t("table.disabled")}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>
                              {t("table.actions")}
                            </DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => openEdit(category)}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              {t("actions.edit")}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleDelete(category._id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              {t("actions.delete")}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="flex flex-col items-center justify-center py-16">
                <div className="mb-6 rounded-full bg-gray-100 p-6">
                  <Tag className="h-12 w-12 text-gray-400" />
                </div>
                <h3 className="mb-2 font-semibold text-gray-900 text-xl">
                  {t("empty.title")}
                </h3>
                <p className="mb-6 max-w-md text-center text-gray-600">
                  {t("empty.description")}
                </p>
                <Button
                  onClick={() => setIsCreateOpen(true)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  {t("actions.create")}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Edit Dialog */}
        <Dialog
          open={!!editingCategory}
          onOpenChange={(open) => !open && setEditingCategory(null)}
        >
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>{t("edit_dialog.title")}</DialogTitle>
              <DialogDescription>
                {t("edit_dialog.description")}
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              <div>
                <Label className="font-medium text-base">
                  {t("form.name")}
                </Label>
                <p className="mb-3 text-muted-foreground text-sm">
                  {t("form.name_help")}
                </p>
                {renderLanguageInputs("name")}
              </div>

              <div>
                <Label className="font-medium text-base">
                  {t("form.description")}
                </Label>
                <p className="mb-3 text-muted-foreground text-sm">
                  {t("form.description_help")}
                </p>
                {renderLanguageInputs("description")}
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="edit-active"
                  checked={formData.isActive}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      isActive: e.target.checked,
                    }))
                  }
                />
                <Label htmlFor="edit-active">{t("form.is_active")}</Label>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setEditingCategory(null)}
              >
                {t("actions.cancel")}
              </Button>
              <Button onClick={handleUpdate}>{t("actions.save")}</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
