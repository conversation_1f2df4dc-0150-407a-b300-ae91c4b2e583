import { ConvexAuthNextjsServerProvider } from "@convex-dev/auth/nextjs/server";
import type { Metadata } from "next";
import { Providers } from "@/components/providers";
import "./styles/globals.css";
import { getLocale } from "next-intl/server";

export const metadata: Metadata = {
  title: "Todo List | 任务管理应用",
  description: "基于Convex的实时任务管理应用，轻松管理你的日常任务",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();
  return (
    <ConvexAuthNextjsServerProvider>
      <html lang={locale} suppressHydrationWarning>
        <body>
          <Providers>{children}</Providers>
        </body>
      </html>
    </ConvexAuthNextjsServerProvider>
  );
}
