import { NodeViewWrapper, type ReactNodeViewProps } from "@tiptap/react";
import { useState } from "react";
import { ResizableImage } from "./resizable-image";

export function ResizableImageComponent(props: ReactNodeViewProps) {
  const { node, updateAttributes, selected } = props;
  const [isLocked, setIsLocked] = useState(false);

  const handleResize = (width: number, height: number) => {
    updateAttributes({
      width: Math.round(width),
      height: Math.round(height),
    });
  };

  const toggleLock = () => {
    setIsLocked(!isLocked);
  };

  const handleAlignChange = (align: "left" | "center" | "right") => {
    updateAttributes({
      align: align,
    });
  };

  // Get alignment styles for the wrapper
  const getWrapperStyles = () => {
    const align = node.attrs.align || "left";
    const baseStyle = {
      display: node.attrs.inline ? "inline-block" : "block",
      margin: "16px 0",
    };

    switch (align) {
      case "center":
        return {
          ...baseStyle,
          display: "flex",
          justifyContent: "center",
          width: "100%",
        };
      case "right":
        return {
          ...baseStyle,
          display: "flex",
          justifyContent: "flex-end",
          width: "100%",
        };
      default:
        return baseStyle;
    }
  };

  return (
    <NodeViewWrapper className="relative" style={getWrapperStyles()}>
      <div className="relative">
        <ResizableImage
          src={node.attrs.src || ""}
          alt={node.attrs.alt || ""}
          onResize={handleResize}
          maintainAspectRatio={isLocked}
          className="rounded-lg"
          style={{
            width: node.attrs.width || 300,
            height: node.attrs.height || 200,
          }}
        />
        {selected && (
          <div className="-top-8 absolute left-0 flex items-center gap-2 rounded-md border border-gray-200 bg-white px-2 py-1 shadow-sm">
            <button
              type="button"
              onClick={toggleLock}
              className="rounded bg-gray-100 px-2 py-1 text-xs transition-colors hover:bg-gray-200"
              title={isLocked ? "解锁长宽比" : "锁定长宽比"}
            >
              {isLocked ? "🔒" : "🔓"}
            </button>

            <div className="flex items-center gap-1 border-gray-300 border-l pl-2">
              <button
                type="button"
                onClick={() => handleAlignChange("left")}
                className={`rounded px-2 py-1 text-xs transition-colors hover:bg-gray-200 ${
                  (node.attrs.align || "left") === "left"
                    ? "bg-blue-100 text-blue-600"
                    : "bg-gray-100"
                }`}
                title="左对齐"
              >
                ⬅️
              </button>
              <button
                type="button"
                onClick={() => handleAlignChange("center")}
                className={`rounded px-2 py-1 text-xs transition-colors hover:bg-gray-200 ${
                  node.attrs.align === "center"
                    ? "bg-blue-100 text-blue-600"
                    : "bg-gray-100"
                }`}
                title="居中对齐"
              >
                ⬆️
              </button>
              <button
                type="button"
                onClick={() => handleAlignChange("right")}
                className={`rounded px-2 py-1 text-xs transition-colors hover:bg-gray-200 ${
                  node.attrs.align === "right"
                    ? "bg-blue-100 text-blue-600"
                    : "bg-gray-100"
                }`}
                title="右对齐"
              >
                ➡️
              </button>
            </div>

            <span className="border-gray-300 border-l pl-2 text-gray-500 text-xs">
              {Math.round(node.attrs.width || 300)} ×{" "}
              {Math.round(node.attrs.height || 200)}
            </span>
          </div>
        )}
      </div>
    </NodeViewWrapper>
  );
}
