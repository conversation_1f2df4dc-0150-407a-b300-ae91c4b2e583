import { Slot } from "@radix-ui/react-slot";
import { ChevronRight, Home, MoreHorizontal } from "lucide-react";
import Link from "next/link";
import * as React from "react";

import { cn } from "@/lib/utils";

// Custom Breadcrumb with smart navigation
interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface SmartBreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function SmartBreadcrumb({ items, className }: SmartBreadcrumbProps) {
  return (
    <nav
      aria-label="Breadcrumb"
      className={cn("flex items-center space-x-1 text-sm", className)}
    >
      <Link
        href="/dashboard"
        className="flex items-center text-muted-foreground transition-colors hover:text-foreground"
      >
        <Home className="h-4 w-4" />
        <span className="sr-only">Dashboard</span>
      </Link>

      {items.map((item, index) => (
        <React.Fragment key={`${item.label}-${item.href || index}`}>
          <ChevronRight className="h-4 w-4 text-muted-foreground" />

          {item.href && !item.current ? (
            <Link
              href={item.href}
              className="text-muted-foreground transition-colors hover:text-foreground"
            >
              {item.label}
            </Link>
          ) : (
            <span
              className={cn(
                "font-medium",
                item.current ? "text-foreground" : "text-muted-foreground",
              )}
            >
              {item.label}
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
}

// Hook to generate breadcrumb items based on pathname
export function useBreadcrumb(
  pathname: string,
  customItems?: BreadcrumbItem[],
) {
  return React.useMemo(() => {
    if (customItems) return customItems;

    const segments = pathname.split("/").filter(Boolean);
    const breadcrumbItems: BreadcrumbItem[] = [];

    let currentPath = "";

    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i];
      currentPath += `/${segment}`;

      // Skip the first segment if it's "dashboard"
      if (i === 0 && segment === "dashboard") continue;

      const isLast = i === segments.length - 1;

      // Generate human-readable labels
      let label = segment;

      switch (segment) {
        case "news":
          label = "新闻管理";
          break;
        case "create":
          label = "创建文章";
          break;
        case "edit":
          label = "编辑文章";
          break;
        case "categories":
          label = "分类管理";
          break;
        case "models":
          label = "模型管理";
          break;
        case "users":
          label = "用户管理";
          break;
        case "settings":
          label = "设置";
          break;
        case "analytics":
          label = "数据分析";
          break;
        case "general":
          label = "通用设置";
          break;
        default:
          // If it looks like an ID, show "详情"
          if (/^[a-z0-9]{20,}$/i.test(segment)) {
            label = "详情";
          } else {
            // Capitalize first letter
            label = segment.charAt(0).toUpperCase() + segment.slice(1);
          }
      }

      breadcrumbItems.push({
        label,
        href: isLast ? undefined : currentPath,
        current: isLast,
      });
    }

    return breadcrumbItems;
  }, [pathname, customItems]);
}

// Original Shadcn/ui breadcrumb components
function Breadcrumb({ className, ...props }: React.ComponentProps<"nav">) {
  return (
    <nav
      aria-label="breadcrumb"
      data-slot="breadcrumb"
      className={className}
      {...props}
    />
  );
}

function BreadcrumbList({ className, ...props }: React.ComponentProps<"ol">) {
  return (
    <ol
      data-slot="breadcrumb-list"
      className={cn(
        "flex flex-wrap items-center gap-1.5 break-words text-muted-foreground text-sm sm:gap-2.5",
        className,
      )}
      {...props}
    />
  );
}

function BreadcrumbItem({ className, ...props }: React.ComponentProps<"li">) {
  return (
    <li
      data-slot="breadcrumb-item"
      className={cn("inline-flex items-center gap-1.5", className)}
      {...props}
    />
  );
}

function BreadcrumbLink({
  asChild,
  className,
  ...props
}: React.ComponentProps<"a"> & {
  asChild?: boolean;
}) {
  const Comp = asChild ? Slot : "a";

  return (
    <Comp
      data-slot="breadcrumb-link"
      className={cn("transition-colors hover:text-foreground", className)}
      {...props}
    />
  );
}

function BreadcrumbPage({ className, ...props }: React.ComponentProps<"span">) {
  return (
    // biome-ignore lint/a11y/useFocusableInteractive: expected
    <span
      data-slot="breadcrumb-page"
      role="link"
      aria-disabled="true"
      aria-current="page"
      className={cn("font-normal text-foreground", className)}
      {...props}
    />
  );
}

function BreadcrumbSeparator({
  children,
  className,
  ...props
}: React.ComponentProps<"li">) {
  return (
    <li
      data-slot="breadcrumb-separator"
      role="presentation"
      aria-hidden="true"
      className={cn("[&>svg]:size-3.5", className)}
      {...props}
    >
      {children ?? <ChevronRight />}
    </li>
  );
}

function BreadcrumbEllipsis({
  className,
  ...props
}: React.ComponentProps<"span">) {
  return (
    <span
      data-slot="breadcrumb-ellipsis"
      role="presentation"
      aria-hidden="true"
      className={cn("flex size-9 items-center justify-center", className)}
      {...props}
    >
      <MoreHorizontal className="size-4" />
      <span className="sr-only">More</span>
    </span>
  );
}

export {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbEllipsis,
};
