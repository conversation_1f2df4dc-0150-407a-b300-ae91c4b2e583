import { cookies } from "next/headers";
import { hasLocale } from "next-intl";
import { getRequestConfig } from "next-intl/server";
import { routing } from "./routing";

export default getRequestConfig(async ({ requestLocale }) => {
  // First try to get locale from the request
  let requested = await requestLocale;

  // If not available, try to get from cookies
  if (!requested) {
    const cookieStore = await cookies();
    requested = cookieStore.get("NEXT_LOCALE")?.value;
  }

  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;

  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default,
  };
});
