"use client";

import { api } from "@convex/api";
import { useMutation, useQuery } from "convex/react";
import type { GenericId } from "convex/values";
import {
  AlertCircle,
  Archive,
  ArrowLeft,
  Calendar,
  CheckCircle,
  Clock,
  Edit,
  Eye,
  Globe,
  Languages,
  MoreHorizontal,
  Tag,
  Trash2,
} from "lucide-react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { SUPPORTED_LANGUAGES } from "@/constants/languages";

export default function NewsDetailPage() {
  const params = useParams();
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations("news");
  const tDetail = useTranslations("news.detail");

  const [selectedLanguage, setSelectedLanguage] = useState<string>(locale);
  const [isDeleting, setIsDeleting] = useState(false);

  const articleId = params.id as string;

  // Validate the articleId format - Convex IDs have a specific format
  const isValidId =
    articleId && articleId.length > 10 && !articleId.includes("categories");

  // Queries and mutations
  const article = useQuery(
    api.news.getNewsArticle,
    isValidId
      ? {
          articleId: articleId as GenericId<"news_articles">,
          language: selectedLanguage,
        }
      : "skip",
  );

  const deleteArticle = useMutation(api.news.deleteNewsArticle);
  const publishArticle = useMutation(api.news.publishNewsArticle);
  const incrementViews = useMutation(api.news.incrementArticleViews);

  // Increment view count on load
  useEffect(() => {
    if (articleId && isValidId) {
      incrementViews({ articleId: articleId as GenericId<"news_articles"> });
    }
  }, [articleId, isValidId, incrementViews]);

  const handleDelete = async () => {
    if (!articleId) return;

    setIsDeleting(true);
    try {
      await deleteArticle({
        articleId: articleId as GenericId<"news_articles">,
      });
      toast.success(tDetail("publish_success"));
      router.push("/dashboard/news");
    } catch (error) {
      console.error("Delete failed:", error);
      toast.error(tDetail("delete_failed"));
    } finally {
      setIsDeleting(false);
    }
  };

  const handlePublish = async () => {
    if (!articleId) return;

    try {
      await publishArticle({
        articleId: articleId as GenericId<"news_articles">,
      });
      toast.success(tDetail("publish_success"));
      // Refresh the article data
      window.location.reload();
    } catch (error) {
      console.error("Publish failed:", error);
      toast.error(tDetail("publish_failed"));
    }
  };

  const getStatusConfig = (status?: string) => {
    switch (status) {
      case "published":
        return {
          variant: "default" as const,
          icon: CheckCircle,
          color: "text-green-700",
          bgColor: "bg-green-100 text-green-700 border-green-200",
        };
      case "draft":
        return {
          variant: "secondary" as const,
          icon: Edit,
          color: "text-orange-700",
          bgColor: "bg-orange-100 text-orange-700 border-orange-200",
        };
      case "archived":
        return {
          variant: "outline" as const,
          icon: Archive,
          color: "text-gray-700",
          bgColor: "bg-gray-100 text-gray-700 border-gray-200",
        };
      default:
        return {
          variant: "secondary" as const,
          icon: AlertCircle,
          color: "text-gray-700",
          bgColor: "bg-gray-100 text-gray-700 border-gray-200",
        };
    }
  };

  const formatDate = (timestamp?: number) => {
    if (!timestamp) return tDetail("not_found");
    return new Date(timestamp).toLocaleDateString(locale, {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const calculateReadingTime = (content: string) => {
    const wordsPerMinute = 200;
    const words = content.replace(/<[^>]*>/g, "").split(/\s+/).length;
    return Math.ceil(words / wordsPerMinute);
  };

  const getCurrentLanguageInfo = () => {
    return SUPPORTED_LANGUAGES.find((lang) => lang.code === selectedLanguage);
  };

  // Handle invalid IDs
  if (!isValidId) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto py-16">
          <div className="flex min-h-[60vh] items-center justify-center">
            <Card className="w-full max-w-lg border-none bg-white shadow-sm">
              <CardContent className="pt-8 text-center">
                <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-red-50">
                  <AlertCircle className="h-8 w-8 text-red-500" />
                </div>
                <h2 className="mb-3 font-semibold text-gray-900 text-xl">
                  {tDetail("not_found")}
                </h2>
                <p className="mb-6 text-gray-600">
                  {tDetail("not_found_description")}
                </p>
                <Link href="/dashboard/news">
                  <Button variant="outline" className="gap-2">
                    <ArrowLeft className="h-4 w-4" />
                    {tDetail("actions.back")}
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto py-16">
          <div className="flex min-h-[60vh] items-center justify-center">
            <Card className="w-full max-w-lg border-none bg-white shadow-sm">
              <CardContent className="pt-8 text-center">
                <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-50">
                  <div className="h-6 w-6 animate-spin rounded-full border-2 border-blue-200 border-t-blue-600" />
                </div>
                <h2 className="mb-3 font-semibold text-gray-900 text-xl">
                  {tDetail("loading")}
                </h2>
                <p className="text-gray-600">
                  {tDetail("shared.errors.loading_content")}
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  const availableLanguages = article.availableLanguages || [
    article.originalLanguage,
  ];
  const currentLanguageInfo = getCurrentLanguageInfo();
  const isOriginalLanguage = selectedLanguage === article.originalLanguage;
  const readingTime = calculateReadingTime(article.content);
  const statusConfig = getStatusConfig(article.status);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto space-y-6 py-8">
        {/* Header */}
        <div className="rounded-xl border border-gray-100 bg-white p-6 shadow-sm">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard/news">
                <Button
                  variant="ghost"
                  size="sm"
                  className="gap-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span className="font-medium">{tDetail("actions.back")}</span>
                </Button>
              </Link>

              <Separator orientation="vertical" className="h-6 bg-gray-200" />

              <div className="flex flex-wrap items-center gap-3">
                <Badge
                  variant={statusConfig.variant}
                  className={`${statusConfig.bgColor} border px-3 py-1 font-medium`}
                >
                  <statusConfig.icon className="mr-1.5 h-3.5 w-3.5" />
                  {t(`status.${article.status || "draft"}`)}
                </Badge>

                {currentLanguageInfo && (
                  <Badge
                    variant="outline"
                    className="gap-1.5 border-gray-200 bg-gray-50 px-3 py-1 font-medium text-gray-700"
                  >
                    <Languages className="h-3.5 w-3.5" />
                    <span className="text-sm">{currentLanguageInfo.flag}</span>
                    <span className="font-medium">
                      {currentLanguageInfo.nativeName}
                    </span>
                    {isOriginalLanguage && (
                      <span className="ml-1 font-normal text-gray-500 text-xs">
                        ({tDetail("original_language")})
                      </span>
                    )}
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* Language Selector */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="gap-2 border-gray-200 font-medium text-gray-700 hover:bg-gray-50"
                  >
                    <Globe className="h-4 w-4" />
                    <span className="text-sm">{currentLanguageInfo?.flag}</span>
                    <span>{currentLanguageInfo?.nativeName}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel className="flex items-center gap-2 font-medium text-gray-900">
                    <Languages className="h-4 w-4" />
                    {tDetail("language_selector")}
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {availableLanguages.map((langCode) => {
                    const lang = SUPPORTED_LANGUAGES.find(
                      (l) => l.code === langCode,
                    );
                    const isOriginal = langCode === article.originalLanguage;
                    const isCurrent = langCode === selectedLanguage;

                    return (
                      <DropdownMenuItem
                        key={langCode}
                        onClick={() => setSelectedLanguage(langCode)}
                        className={`${isCurrent ? "bg-blue-50 text-blue-900" : "text-gray-700"} cursor-pointer`}
                      >
                        <div className="flex w-full items-center justify-between">
                          <div className="flex items-center gap-3">
                            <span className="text-base">{lang?.flag}</span>
                            <div>
                              <div className="font-medium">
                                {lang?.nativeName}
                              </div>
                              {isOriginal && (
                                <div className="text-muted-foreground text-xs">
                                  {tDetail("original_language")}
                                </div>
                              )}
                            </div>
                          </div>
                          {isCurrent && (
                            <CheckCircle className="h-4 w-4 text-blue-600" />
                          )}
                        </div>
                      </DropdownMenuItem>
                    );
                  })}
                </DropdownMenuContent>
              </DropdownMenu>

              {article.status === "draft" && (
                <Button
                  onClick={handlePublish}
                  className="gap-2 bg-blue-600 font-medium text-white hover:bg-blue-700"
                >
                  <Eye className="h-4 w-4" />
                  {tDetail("actions.publish")}
                </Button>
              )}

              {/* Actions Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    className="border-gray-200 text-gray-700 hover:bg-gray-50"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel className="font-medium text-gray-900">
                    {tDetail("actions.title")}
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />

                  <DropdownMenuItem asChild>
                    <Link
                      href={`/dashboard/news/${articleId}/edit`}
                      className="cursor-pointer text-gray-700"
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      {tDetail("actions.edit_article")}
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <DropdownMenuItem
                        className="cursor-pointer text-red-600 focus:text-red-700"
                        onSelect={(e) => e.preventDefault()}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {tDetail("actions.delete")}
                      </DropdownMenuItem>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle className="text-gray-900">
                          {tDetail("confirm_delete")}
                        </AlertDialogTitle>
                        <AlertDialogDescription className="text-gray-600">
                          {tDetail("delete_warning")}
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel className="text-gray-700">
                          {tDetail("delete_cancel")}
                        </AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDelete}
                          disabled={isDeleting}
                          className="bg-red-600 text-white hover:bg-red-700"
                        >
                          {isDeleting
                            ? tDetail("deleting")
                            : tDetail("delete_confirm")}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
          {/* Main Content */}
          <div className="lg:col-span-3">
            <Card className="border-none bg-white shadow-sm">
              <CardContent className="p-0">
                {/* Article Header */}
                <div className="border-gray-100 border-b p-8">
                  <div className="mb-4 flex flex-wrap gap-2">
                    <Badge variant="secondary" className="gap-1">
                      <Tag className="h-3 w-3" />
                      {article.category.name}
                    </Badge>

                    <Badge variant="outline" className="gap-1">
                      <Clock className="h-3 w-3" />
                      {tDetail("reading_time", { minutes: readingTime })}
                    </Badge>

                    <Badge variant="outline" className="gap-1">
                      <Eye className="h-3 w-3" />
                      {tDetail("views", { count: article.viewCount || 0 })}
                    </Badge>
                  </div>

                  <h1 className="mb-4 font-bold text-3xl text-gray-900 leading-tight md:text-4xl">
                    {article.title}
                  </h1>

                  {article.summary && (
                    <p className="text-gray-600 text-lg leading-relaxed">
                      {article.summary}
                    </p>
                  )}
                </div>

                {/* Article Meta */}
                <div className="border-gray-100 border-b px-8 py-4">
                  <div className="flex items-center gap-2 text-gray-500 text-sm">
                    <Calendar className="h-4 w-4" />
                    <span>
                      {article.publishedAt
                        ? `${tDetail("published_on")} ${formatDate(article.publishedAt)}`
                        : `${tDetail("created_on")} ${formatDate(article._creationTime)}`}
                    </span>
                  </div>
                </div>

                {/* Article Content */}
                <div className="p-8">
                  <div className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-p:text-gray-700">
                    <div
                      className="leading-relaxed"
                      // biome-ignore lint/security/noDangerouslySetInnerHtml: Content is from controlled source
                      dangerouslySetInnerHTML={{ __html: article.content }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Article Info */}
            <Card className="border-none bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-900 text-lg">
                  {tDetail("sidebar.publication_info")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    {tDetail("sidebar.status")}
                  </span>
                  <Badge
                    variant={statusConfig.variant}
                    className={`${statusConfig.bgColor} border font-medium`}
                  >
                    <statusConfig.icon className="mr-1 h-3 w-3" />
                    {t(`status.${article.status || "draft"}`)}
                  </Badge>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    {tDetail("sidebar.category")}
                  </span>
                  <span className="font-medium text-gray-900">
                    {article.category.name}
                  </span>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    {tDetail("sidebar.views")}
                  </span>
                  <div className="flex items-center gap-1 font-medium text-gray-900">
                    <Eye className="h-3 w-3" />
                    {article.viewCount || 0}
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    {tDetail("sidebar.reading_time")}
                  </span>
                  <div className="flex items-center gap-1 font-medium text-gray-900">
                    <Clock className="h-3 w-3" />
                    {tDetail("reading_time", { minutes: readingTime })}
                  </div>
                </div>

                <Separator />

                <div className="text-gray-500 text-xs">
                  <div className="mb-1">
                    <strong>{tDetail("sidebar.created")}:</strong>{" "}
                    {formatDate(article._creationTime)}
                  </div>
                  {article.publishedAt && (
                    <div>
                      <strong>{tDetail("sidebar.published")}:</strong>{" "}
                      {formatDate(article.publishedAt)}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Language Versions */}
            <Card className="border-none bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-gray-900 text-lg">
                  <Languages className="h-5 w-5" />
                  {tDetail("sidebar.language_versions")}
                </CardTitle>
                <CardDescription>
                  {tDetail("available_in", {
                    count: availableLanguages.length,
                  })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-32">
                  <div className="space-y-2">
                    {availableLanguages.map((langCode) => {
                      const lang = SUPPORTED_LANGUAGES.find(
                        (l) => l.code === langCode,
                      );
                      const isOriginal = langCode === article.originalLanguage;
                      const isCurrent = langCode === selectedLanguage;

                      return (
                        <button
                          key={langCode}
                          type="button"
                          onClick={() => setSelectedLanguage(langCode)}
                          className={`flex w-full items-center justify-between rounded-lg border p-3 text-left transition-colors ${
                            isCurrent
                              ? "border-blue-200 bg-blue-50"
                              : "border-gray-200 hover:bg-gray-50"
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <span>{lang?.flag}</span>
                            <div>
                              <div className="font-medium text-gray-900 text-sm">
                                {lang?.nativeName}
                              </div>
                              {isOriginal && (
                                <Badge
                                  variant="outline"
                                  className="mt-1 text-xs"
                                >
                                  {tDetail("original_language")}
                                </Badge>
                              )}
                            </div>
                          </div>
                          {isCurrent && (
                            <CheckCircle className="h-4 w-4 text-blue-600" />
                          )}
                        </button>
                      );
                    })}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
