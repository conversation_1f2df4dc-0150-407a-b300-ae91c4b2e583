import { routing } from "./routing";

// 从 routing 配置中自动推断语言类型
type Locale = (typeof routing.locales)[number];

export function getLocaleFromCookie(): string {
  if (typeof window === "undefined") return routing.defaultLocale;

  const cookies = document.cookie.split(";");
  const localeCookie = cookies.find((cookie) =>
    cookie.trim().startsWith("NEXT_LOCALE="),
  );

  if (localeCookie) {
    const locale = localeCookie.split("=")[1];
    return routing.locales.includes(locale as Locale)
      ? locale
      : routing.defaultLocale;
  }

  return routing.defaultLocale;
}

export function setLocaleCookie(locale: string): void {
  if (!routing.locales.includes(locale as Locale)) {
    console.warn(
      `Locale ${locale} is not supported. Available locales: ${routing.locales.join(", ")}`,
    );
    return;
  }

  const cookie = `NEXT_LOCALE=${locale}; path=/; max-age=********; SameSite=lax`;
  if (typeof document !== "undefined") {
    document.cookie = cookie;
  }
}

export function toggleLocale(currentLocale: string): string {
  const currentIndex = routing.locales.indexOf(currentLocale as Locale);
  const nextIndex = (currentIndex + 1) % routing.locales.length;
  return routing.locales[nextIndex];
}
