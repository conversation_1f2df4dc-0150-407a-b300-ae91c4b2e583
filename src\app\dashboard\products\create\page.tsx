"use client";

import { api } from "@convex/api";
import { useMutation, useQuery } from "convex/react";
import {
  AlertCircle,
  ArrowLeft,
  Building2,
  Check,
  Eye,
  Globe,
  ImageIcon,
  Layers,
  Package,
  Plus,
  Save,
  Settings,
  Star,
  Tag,
  Upload,
  X,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MediaPicker } from "@/components/ui/media-picker";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { SUPPORTED_LANGUAGES } from "@/constants/languages";

type ProductFormData = {
  name: Record<string, string>;
  title: Record<string, string>;
  description: Record<string, string>;
  specifications: Record<string, string>;
  instructions: Record<string, string>;
  categoryId: string;
  status: "draft" | "published";
  isFeatured: boolean;
  coverImageId?: any;
  imageIds?: any[];
  seoKeywords: Record<string, string[]>;
  seoDescription: Record<string, string>;
  slug: Record<string, string>;
  originalLanguage: string;
  availableLanguages: string[];
};

type ProductStandard = {
  type: Record<string, string>;
  values: Record<string, string[]>;
};

export default function CreateProductPage() {
  const t = useTranslations("products");
  const locale = useLocale();
  const router = useRouter();
  const [currentLanguage, setCurrentLanguage] = useState<string>(locale);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCoverImagePickerOpen, setIsCoverImagePickerOpen] = useState(false);
  const [isImagePickerOpen, setIsImagePickerOpen] = useState(false);

  const [formData, setFormData] = useState<ProductFormData>({
    name: {},
    title: {},
    description: {},
    specifications: {},
    instructions: {},
    categoryId: "",
    status: "draft",
    isFeatured: false,
    imageIds: [],
    seoKeywords: {},
    seoDescription: {},
    slug: {},
    originalLanguage: locale,
    availableLanguages: [locale],
  });

  const [standards, setStandards] = useState<ProductStandard[]>([]);

  // Queries
  const categories = useQuery(api.productCategories.getActiveCategories, {
    language: currentLanguage,
  });

  // Query for cover image info
  const coverImageInfo = useQuery(
    api.media.getMediaFileByStorageId,
    formData.coverImageId ? { storageId: formData.coverImageId } : "skip",
  );

  // Query for additional images info
  const imageInfos = useQuery(
    api.media.getMediaFilesByStorageIds,
    formData.imageIds && formData.imageIds.length > 0
      ? { storageIds: formData.imageIds }
      : "skip",
  );

  // Mutations
  const createProduct = useMutation(api.products.createProduct);
  const batchSetStandards = useMutation(
    api.productStandards.batchSetProductStandards,
  );

  // Add language to available languages if not already present
  const addLanguage = (langCode: string) => {
    if (!formData.availableLanguages.includes(langCode)) {
      setFormData((prev) => ({
        ...prev,
        availableLanguages: [...prev.availableLanguages, langCode],
      }));
    }
    setCurrentLanguage(langCode);
  };

  // Remove language from available languages
  const removeLanguage = (langCode: string) => {
    if (langCode === formData.originalLanguage) {
      toast.error("无法删除原始语言");
      return;
    }
    setFormData((prev) => {
      const { [langCode]: _, ...restName } = prev.name;
      const { [langCode]: __, ...restTitle } = prev.title;
      const { [langCode]: ___, ...restDescription } = prev.description;
      const { [langCode]: ____, ...restSpecifications } = prev.specifications;
      const { [langCode]: _____, ...restInstructions } = prev.instructions;
      const { [langCode]: ______, ...restSeoKeywords } = prev.seoKeywords;
      const { [langCode]: _______, ...restSeoDescription } =
        prev.seoDescription;
      const { [langCode]: ________, ...restSlug } = prev.slug;

      return {
        ...prev,
        availableLanguages: prev.availableLanguages.filter(
          (lang) => lang !== langCode,
        ),
        name: restName,
        title: restTitle,
        description: restDescription,
        specifications: restSpecifications,
        instructions: restInstructions,
        seoKeywords: restSeoKeywords,
        seoDescription: restSeoDescription,
        slug: restSlug,
      };
    });
    if (currentLanguage === langCode) {
      setCurrentLanguage(formData.originalLanguage);
    }
  };

  // Update form field
  const updateField = (
    field: keyof ProductFormData,
    value: string | boolean | string[],
    language?: string,
  ) => {
    if (
      language &&
      (field === "name" ||
        field === "title" ||
        field === "description" ||
        field === "specifications" ||
        field === "instructions" ||
        field === "seoDescription" ||
        field === "slug")
    ) {
      setFormData((prev) => ({
        ...prev,
        [field]: {
          ...prev[field],
          [language]: value,
        },
      }));
    } else if (language && field === "seoKeywords") {
      const keywords =
        typeof value === "string"
          ? value
              .split(",")
              .map((k: string) => k.trim())
              .filter((k: string) => k)
          : [];
      setFormData((prev) => ({
        ...prev,
        [field]: {
          ...prev[field],
          [language]: keywords,
        },
      }));
    } else {
      setFormData((prev) => ({ ...prev, [field]: value }));
    }
  };

  // Add new standard
  const addStandard = () => {
    setStandards((prev) => [...prev, { type: {}, values: {} }]);
  };

  // Update standard
  const updateStandard = (
    index: number,
    field: "type" | "values",
    language: string,
    value: string,
  ) => {
    setStandards((prev) =>
      prev.map((standard, i) => {
        if (i === index) {
          if (field === "values") {
            return {
              ...standard,
              [field]: {
                ...standard[field],
                [language]: value
                  .split(",")
                  .map((v) => v.trim())
                  .filter((v) => v),
              },
            };
          } else {
            return {
              ...standard,
              [field]: {
                ...standard[field],
                [language]: value,
              },
            };
          }
        }
        return standard;
      }),
    );
  };

  // Remove standard
  const removeStandard = (index: number) => {
    setStandards((prev) => prev.filter((_, i) => i !== index));
  };

  // Handle cover image selection
  const handleCoverImageSelect = (file: any) => {
    setFormData((prev) => ({
      ...prev,
      coverImageId: file.storageId,
    }));
  };

  // Handle multiple images selection
  const handleImageSelect = (file: any) => {
    setFormData((prev) => ({
      ...prev,
      imageIds: [...(prev.imageIds || []), file.storageId],
    }));
  };

  // Remove image from gallery
  const removeImage = (storageId: string) => {
    setFormData((prev) => ({
      ...prev,
      imageIds: prev.imageIds?.filter((id) => id !== storageId) || [],
    }));
  };

  // Remove cover image
  const removeCoverImage = () => {
    setFormData((prev) => ({
      ...prev,
      coverImageId: undefined,
    }));
  };

  // Validate form
  const validateForm = () => {
    if (!formData.categoryId) {
      toast.error(t("shared.errors.select_category"));
      return false;
    }

    const hasName = Object.values(formData.name).some((name) => name?.trim());
    if (!hasName) {
      toast.error(t("shared.errors.fill_name"));
      return false;
    }

    const hasDescription = Object.values(formData.description).some((desc) =>
      desc?.trim(),
    );
    if (!hasDescription) {
      toast.error(t("shared.errors.fill_description"));
      return false;
    }

    return true;
  };

  // Submit form
  const handleSubmit = async (publish: boolean = false) => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      // Clean up form data
      const cleanFormData = {
        ...formData,
        status: publish ? ("published" as const) : ("draft" as const),
      };

      // Create product
      const productId = await createProduct(cleanFormData as any);

      // Add standards if any
      if (standards.length > 0) {
        const validStandards = standards
          .filter(
            (standard) =>
              Object.values(standard.type).some((type) => type?.trim()) &&
              Object.values(standard.values).some(
                (values) => values?.length > 0,
              ),
          )
          .map((standard) => ({
            standardType: standard.type,
            standardValues: standard.values,
            originalLanguage: formData.originalLanguage,
            availableLanguages: formData.availableLanguages,
          }));

        if (validStandards.length > 0) {
          await batchSetStandards({
            productId,
            standards: validStandards,
          });
        }
      }

      toast.success(
        publish
          ? t("shared.errors.published_success")
          : t("shared.errors.draft_saved"),
      );
      router.push("/dashboard/products");
    } catch (_error) {
      toast.error(t("shared.errors.save_failed_generic"));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Calculate completion percentage
  const calculateCompletion = () => {
    let completed = 0;
    let total = 0;

    // Required fields
    total += 3; // name, description, category
    if (formData.name[currentLanguage]?.trim()) completed++;
    if (formData.description[currentLanguage]?.trim()) completed++;
    if (formData.categoryId) completed++;

    // Optional fields
    total += 4; // title, specifications, instructions, cover
    if (formData.title[currentLanguage]?.trim()) completed++;
    if (formData.specifications[currentLanguage]?.trim()) completed++;
    if (formData.instructions[currentLanguage]?.trim()) completed++;
    if (formData.coverImageId) completed++;

    return Math.round((completed / total) * 100);
  };

  const completionPercentage = calculateCompletion();
  const activeLanguageInfo = SUPPORTED_LANGUAGES.find(
    (lang) => lang.code === currentLanguage,
  );

  return (
    <div className="space-y-6">
      {/* Header Actions - Responsive Layout */}
      <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
        {/* Left Section - Title and Navigation */}
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          <Link href="/dashboard/products">
            <Button variant="ghost" size="sm" className="gap-2 self-start">
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden sm:inline">
                {t("shared.ui.back_to_list")}
              </span>
              <span className="sm:hidden">{t("shared.ui.back")}</span>
            </Button>
          </Link>

          <div className="hidden h-6 w-px bg-gray-300 sm:block" />

          <div>
            <h1 className="font-bold text-gray-900 text-xl sm:text-2xl">
              {t("create.title")}
            </h1>
            <p className="text-gray-600 text-sm">
              {t("shared.ui.currently_editing")}
              {activeLanguageInfo?.nativeName}
            </p>
          </div>
        </div>

        {/* Right Section - Progress and Actions */}
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          {/* Progress Indicator */}
          <div className="flex items-center justify-between text-gray-600 text-sm sm:justify-start sm:space-x-2">
            <span className="sm:hidden">
              {t("shared.ui.completion_progress")}
            </span>
            <div className="flex items-center space-x-2">
              <Progress
                value={completionPercentage}
                className="h-2 w-16 sm:w-20"
              />
              <span className="min-w-12 font-medium">
                {completionPercentage}%
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col gap-2 sm:flex-row sm:space-x-3">
            <Button
              variant="outline"
              onClick={() => handleSubmit(false)}
              disabled={isSubmitting}
              className="order-2 gap-2 sm:order-1"
              size="sm"
            >
              <Save className="h-4 w-4" />
              <span className="hidden sm:inline">
                {t("shared.ui.save_draft")}
              </span>
              <span className="sm:hidden">{t("shared.ui.draft")}</span>
            </Button>
            <Button
              onClick={() => handleSubmit(true)}
              disabled={isSubmitting || completionPercentage < 100}
              className="order-1 gap-2 bg-green-600 hover:bg-green-700 sm:order-2"
              size="sm"
            >
              {isSubmitting ? (
                "发布中..."
              ) : (
                <>
                  <Check className="h-4 w-4" />
                  <span className="hidden sm:inline">
                    {t("shared.ui.publish")}
                  </span>
                  <span className="sm:hidden">{t("shared.ui.publish")}</span>
                </>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 gap-6 lg:gap-8 xl:grid-cols-12">
        {/* Main Content */}
        <div className="space-y-6 xl:col-span-8">
          {/* Language Management */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="flex items-center space-x-3">
                  <div className="rounded-lg bg-blue-100 p-2">
                    <Globe className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">
                      {t("shared.ui.multilang_management")}
                    </CardTitle>
                    <p className="text-gray-600 text-sm">
                      {t("shared.ui.multilang_description")}
                    </p>
                  </div>
                </div>
                <Select
                  value=""
                  onValueChange={(value) => value && addLanguage(value)}
                >
                  <SelectTrigger className="w-full sm:w-48">
                    <Plus className="mr-2 h-4 w-4" />
                    <SelectValue placeholder={t("shared.ui.add_language")} />
                  </SelectTrigger>
                  <SelectContent>
                    {SUPPORTED_LANGUAGES.filter(
                      (lang) =>
                        !formData.availableLanguages.includes(lang.code),
                    ).map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        <span className="flex items-center gap-2">
                          <span>{lang.flag}</span>
                          <span>{lang.nativeName}</span>
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {formData.availableLanguages.map((langCode) => {
                  const lang = SUPPORTED_LANGUAGES.find(
                    (l) => l.code === langCode,
                  );
                  const isOriginal = langCode === formData.originalLanguage;
                  const isActive = langCode === currentLanguage;

                  return (
                    <Badge
                      key={langCode}
                      variant={isActive ? "default" : "secondary"}
                      className={`relative cursor-pointer px-3 py-2 text-sm ${
                        isOriginal ? "ring-2 ring-blue-500" : ""
                      } ${isActive ? "bg-blue-600" : ""}`}
                      onClick={() => setCurrentLanguage(langCode)}
                    >
                      <span className="flex items-center gap-2">
                        <span>{lang?.flag}</span>
                        <span>{lang?.nativeName}</span>
                        {isOriginal && (
                          <span className="rounded bg-white/20 px-1 text-xs">
                            {t("shared.ui.original_badge")}
                          </span>
                        )}
                      </span>
                      {!isOriginal && (
                        <button
                          type="button"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeLanguage(langCode);
                          }}
                          className="ml-2 rounded-full p-0.5 hover:bg-red-500/20"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      )}
                    </Badge>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Cover Image */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="rounded-lg bg-indigo-100 p-2">
                  <ImageIcon className="h-5 w-5 text-indigo-600" />
                </div>
                <div>
                  <span>{t("form.cover_image")}</span>
                  <p className="font-normal text-gray-600 text-sm">
                    选择一张主要的封面图片
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {formData.coverImageId && coverImageInfo?.url ? (
                <div className="space-y-4">
                  <div className="relative aspect-video w-full overflow-hidden rounded-lg">
                    <Image
                      src={coverImageInfo.url}
                      alt="封面图片"
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                    <Button
                      variant="destructive"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={removeCoverImage}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => setIsCoverImagePickerOpen(true)}
                    className="w-full"
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    更换封面图片
                  </Button>
                </div>
              ) : (
                <div className="flex items-center justify-center rounded-lg border-2 border-gray-300 border-dashed px-6 py-4">
                  <div className="text-center">
                    <ImageIcon className="mx-auto h-8 w-8 text-gray-400" />
                    <div className="mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsCoverImagePickerOpen(true)}
                      >
                        <Upload className="mr-2 h-4 w-4" />
                        选择封面图片
                      </Button>
                    </div>
                    <p className="mt-1 text-gray-500 text-xs">
                      从媒体库选择或上传新图片
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Product Images Gallery */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="rounded-lg bg-purple-100 p-2">
                  <Layers className="h-5 w-5 text-purple-600" />
                </div>
                <div className="flex-1">
                  <span>产品图片库</span>
                  <p className="font-normal text-gray-600 text-sm">
                    添加多张产品图片展示更多细节
                  </p>
                </div>
                <Button
                  onClick={() => setIsImagePickerOpen(true)}
                  variant="outline"
                  size="sm"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  添加图片
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {formData.imageIds && formData.imageIds.length > 0 ? (
                <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
                  {formData.imageIds.map((imageId, index) => {
                    const imageInfo = imageInfos?.find(
                      (img) => img.storageId === imageId,
                    );
                    return (
                      <div
                        key={imageId}
                        className="group relative aspect-square overflow-hidden rounded-lg border"
                      >
                        {imageInfo?.url ? (
                          <Image
                            src={imageInfo.url}
                            alt={`产品图片 ${index + 1}`}
                            fill
                            className="object-cover transition-transform group-hover:scale-105"
                            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                          />
                        ) : (
                          <div className="flex h-full w-full items-center justify-center bg-gray-100">
                            <ImageIcon className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                        <Button
                          variant="destructive"
                          size="sm"
                          className="absolute top-1 right-1 opacity-0 transition-opacity group-hover:opacity-100"
                          onClick={() => removeImage(imageId)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="py-8 text-center text-gray-500">
                  <Layers className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                  <p>还没有添加产品图片</p>
                  <Button
                    onClick={() => setIsImagePickerOpen(true)}
                    variant="outline"
                    size="sm"
                    className="mt-2"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    添加第一张图片
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Product Content */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="rounded-lg bg-green-100 p-2">
                  <Package className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <span>{t("shared.ui.product_content")}</span>
                  <p className="font-normal text-gray-600 text-sm">
                    {activeLanguageInfo?.nativeName} {t("shared.ui.version")}
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Product Name */}
              <div>
                <Label htmlFor="name" className="font-medium text-base">
                  {t("form.name")} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  placeholder={t("shared.ui.product_name_placeholder")}
                  value={formData.name[currentLanguage] || ""}
                  onChange={(e) =>
                    updateField("name", e.target.value, currentLanguage)
                  }
                  className="mt-2 h-12 font-medium text-lg"
                />
              </div>

              {/* Product Title */}
              <div>
                <Label htmlFor="title" className="font-medium text-base">
                  {t("form.title")}
                </Label>
                <Input
                  id="title"
                  placeholder={t("shared.ui.product_title_placeholder")}
                  value={formData.title[currentLanguage] || ""}
                  onChange={(e) =>
                    updateField("title", e.target.value, currentLanguage)
                  }
                  className="mt-2"
                />
              </div>

              {/* Product Description */}
              <div>
                <Label htmlFor="description" className="font-medium text-base">
                  {t("form.description")}{" "}
                  <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="description"
                  placeholder={t("shared.ui.product_description_placeholder")}
                  value={formData.description[currentLanguage] || ""}
                  onChange={(e) =>
                    updateField("description", e.target.value, currentLanguage)
                  }
                  rows={4}
                  className="mt-2"
                />
              </div>

              {/* Product Specifications */}
              <div>
                <Label
                  htmlFor="specifications"
                  className="font-medium text-base"
                >
                  {t("form.specifications")}
                </Label>
                <Textarea
                  id="specifications"
                  placeholder={t(
                    "shared.ui.product_specifications_placeholder",
                  )}
                  value={formData.specifications[currentLanguage] || ""}
                  onChange={(e) =>
                    updateField(
                      "specifications",
                      e.target.value,
                      currentLanguage,
                    )
                  }
                  rows={4}
                  className="mt-2"
                />
              </div>

              {/* Instructions */}
              <div>
                <Label htmlFor="instructions" className="font-medium text-base">
                  {t("form.instructions")}
                </Label>
                <Textarea
                  id="instructions"
                  placeholder={t("shared.ui.product_instructions_placeholder")}
                  value={formData.instructions[currentLanguage] || ""}
                  onChange={(e) =>
                    updateField("instructions", e.target.value, currentLanguage)
                  }
                  rows={4}
                  className="mt-2"
                />
              </div>
            </CardContent>
          </Card>

          {/* Product Standards */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="rounded-lg bg-yellow-100 p-2">
                  <Building2 className="h-5 w-5 text-yellow-600" />
                </div>
                <div className="flex-1">
                  <span>{t("standards.title")}</span>
                  <p className="font-normal text-gray-600 text-sm">
                    {t("standards.no_standards_description")}
                  </p>
                </div>
                <Button onClick={addStandard} variant="outline" size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  {t("standards.add_standard")}
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {standards.length === 0 ? (
                <div className="py-8 text-center text-gray-500">
                  <Building2 className="mx-auto mb-2 h-8 w-8 text-gray-400" />
                  <p>{t("standards.no_standards")}</p>
                </div>
              ) : (
                standards.map((standard, index) => (
                  <div
                    key={`product-standard-${index}-${standard.type[currentLanguage] || "new"}`}
                    className="space-y-3 rounded-lg bg-gray-50 p-4"
                  >
                    <div className="flex items-center justify-between">
                      <Label className="font-medium text-sm">
                        标准 {index + 1}
                      </Label>
                      <Button
                        onClick={() => removeStandard(index)}
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:bg-red-50"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="text-sm">
                          {t("standards.standard_type")}
                        </Label>
                        <Input
                          placeholder={t("standards.type_placeholder")}
                          value={standard.type[currentLanguage] || ""}
                          onChange={(e) =>
                            updateStandard(
                              index,
                              "type",
                              currentLanguage,
                              e.target.value,
                            )
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label className="text-sm">
                          {t("standards.standard_values")}
                        </Label>
                        <Input
                          placeholder={t("standards.values_placeholder")}
                          value={
                            standard.values[currentLanguage]?.join(", ") || ""
                          }
                          onChange={(e) =>
                            updateStandard(
                              index,
                              "values",
                              currentLanguage,
                              e.target.value,
                            )
                          }
                        />
                      </div>
                    </div>
                  </div>
                ))
              )}
            </CardContent>
          </Card>

          {/* SEO Settings */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="rounded-lg bg-purple-100 p-2">
                  <Settings className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <span>{t("shared.ui.seo_optimization")}</span>
                  <p className="font-normal text-gray-600 text-sm">
                    {t("shared.ui.seo_description")}
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="slug" className="font-medium text-base">
                  {t("shared.ui.url_slug")}
                </Label>
                <Input
                  id="slug"
                  placeholder={t("shared.ui.url_slug_placeholder")}
                  value={formData.slug[currentLanguage] || ""}
                  onChange={(e) =>
                    updateField("slug", e.target.value, currentLanguage)
                  }
                  className="mt-2"
                />
                <p className="mt-1 text-gray-500 text-xs">
                  {t("shared.ui.url_slug_help")}
                </p>
              </div>

              <div>
                <Label
                  htmlFor="seoDescription"
                  className="font-medium text-base"
                >
                  {t("shared.ui.seo_description_label")}
                </Label>
                <Textarea
                  id="seoDescription"
                  placeholder={t("shared.ui.seo_description_placeholder")}
                  value={formData.seoDescription[currentLanguage] || ""}
                  onChange={(e) =>
                    updateField(
                      "seoDescription",
                      e.target.value,
                      currentLanguage,
                    )
                  }
                  rows={3}
                  className="mt-2"
                />
                <p className="mt-1 text-gray-500 text-xs">
                  {t("shared.ui.seo_description_help")}
                </p>
              </div>

              <div>
                <Label htmlFor="keywords" className="font-medium text-base">
                  {t("shared.ui.keywords")}
                </Label>
                <Input
                  id="keywords"
                  placeholder={t("shared.ui.keywords_placeholder")}
                  value={
                    formData.seoKeywords[currentLanguage]?.join(", ") || ""
                  }
                  onChange={(e) =>
                    updateField("seoKeywords", e.target.value, currentLanguage)
                  }
                  className="mt-2"
                />
                <p className="mt-1 text-gray-500 text-xs">
                  {t("shared.ui.keywords_help")}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6 xl:col-span-4">
          {/* Publishing Options */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="rounded-lg bg-orange-100 p-2">
                  <Layers className="h-5 w-5 text-orange-600" />
                </div>
                <span>{t("shared.ui.publish_settings")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="category" className="font-medium text-base">
                  {t("shared.ui.category")}{" "}
                  <span className="text-red-500">*</span>
                </Label>
                <Select
                  value={formData.categoryId}
                  onValueChange={(value) => updateField("categoryId", value)}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue placeholder={t("shared.ui.select_category")} />
                  </SelectTrigger>
                  <SelectContent>
                    {categories?.map((category) => (
                      <SelectItem key={category._id} value={category._id}>
                        <span className="flex items-center gap-2">
                          <Tag className="h-4 w-4" />
                          {category.name}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="status" className="font-medium text-base">
                  {t("shared.ui.product_status")}
                </Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: "draft" | "published") =>
                    updateField("status", value)
                  }
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">{t("status.draft")}</SelectItem>
                    <SelectItem value="published">
                      {t("status.published")}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="featured"
                  checked={formData.isFeatured}
                  onCheckedChange={(checked) =>
                    updateField("isFeatured", checked)
                  }
                />
                <div className="space-y-1">
                  <Label
                    htmlFor="featured"
                    className="flex items-center gap-2 font-medium text-base"
                  >
                    <Star className="h-4 w-4" />
                    {t("shared.ui.featured_product")}
                  </Label>
                  <p className="text-gray-600 text-xs">
                    {t("shared.ui.featured_description")}
                  </p>
                </div>
              </div>

              <Separator />

              <div>
                <Label
                  htmlFor="original-language"
                  className="font-medium text-base"
                >
                  {t("shared.ui.original_language")}
                </Label>
                <Select
                  value={formData.originalLanguage}
                  onValueChange={(value) =>
                    updateField("originalLanguage", value)
                  }
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {SUPPORTED_LANGUAGES.map((lang) => (
                      <SelectItem key={lang.code} value={lang.code}>
                        <span className="flex items-center gap-2">
                          <span>{lang.flag}</span>
                          <span>{lang.nativeName}</span>
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Completion Status */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center space-x-3">
                <div className="rounded-lg bg-emerald-100 p-2">
                  <Eye className="h-5 w-5 text-emerald-600" />
                </div>
                <span>{t("shared.ui.completion_status")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 text-sm">
                    {t("shared.ui.overall_progress")}
                  </span>
                  <span className="font-medium text-sm">
                    {completionPercentage}%
                  </span>
                </div>
                <Progress value={completionPercentage} className="h-2" />

                <div className="grid grid-cols-1 gap-2 text-xs">
                  <div className="flex items-center space-x-2">
                    {formData.name[currentLanguage] ? (
                      <Check className="h-3 w-3 flex-shrink-0 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 flex-shrink-0 text-gray-400" />
                    )}
                    <span
                      className={
                        formData.name[currentLanguage]
                          ? "text-green-600"
                          : "text-gray-500"
                      }
                    >
                      {t("form.name")}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {formData.description[currentLanguage] ? (
                      <Check className="h-3 w-3 flex-shrink-0 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 flex-shrink-0 text-gray-400" />
                    )}
                    <span
                      className={
                        formData.description[currentLanguage]
                          ? "text-green-600"
                          : "text-gray-500"
                      }
                    >
                      {t("form.description")}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {formData.categoryId ? (
                      <Check className="h-3 w-3 flex-shrink-0 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 flex-shrink-0 text-gray-400" />
                    )}
                    <span
                      className={
                        formData.categoryId ? "text-green-600" : "text-gray-500"
                      }
                    >
                      {t("shared.ui.select_category")}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {formData.title[currentLanguage] ? (
                      <Check className="h-3 w-3 flex-shrink-0 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 flex-shrink-0 text-gray-400" />
                    )}
                    <span
                      className={
                        formData.title[currentLanguage]
                          ? "text-green-600"
                          : "text-gray-500"
                      }
                    >
                      {t("form.title")}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {formData.specifications[currentLanguage] ? (
                      <Check className="h-3 w-3 flex-shrink-0 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 flex-shrink-0 text-gray-400" />
                    )}
                    <span
                      className={
                        formData.specifications[currentLanguage]
                          ? "text-green-600"
                          : "text-gray-500"
                      }
                    >
                      {t("form.specifications")}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {formData.instructions[currentLanguage] ? (
                      <Check className="h-3 w-3 flex-shrink-0 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 flex-shrink-0 text-gray-400" />
                    )}
                    <span
                      className={
                        formData.instructions[currentLanguage]
                          ? "text-green-600"
                          : "text-gray-500"
                      }
                    >
                      {t("form.instructions")}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {formData.coverImageId ? (
                      <Check className="h-3 w-3 flex-shrink-0 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 flex-shrink-0 text-gray-400" />
                    )}
                    <span
                      className={
                        formData.coverImageId
                          ? "text-green-600"
                          : "text-gray-500"
                      }
                    >
                      {t("form.cover_image")}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Media Pickers */}
      <MediaPicker
        open={isCoverImagePickerOpen}
        onOpenChange={setIsCoverImagePickerOpen}
        onSelect={handleCoverImageSelect}
        allowedTypes={["image"]}
        multiple={false}
      />

      <MediaPicker
        open={isImagePickerOpen}
        onOpenChange={setIsImagePickerOpen}
        onSelect={handleImageSelect}
        allowedTypes={["image"]}
        multiple={false}
      />
    </div>
  );
}
