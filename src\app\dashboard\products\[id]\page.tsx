"use client";

import { api } from "@convex/api";
import { useMutation, useQuery } from "convex/react";
import {
  ArrowLeft,
  Building2,
  Calendar,
  Edit,
  Eye,
  Globe,
  MoreHorizontal,
  Star,
  Tag,
  Trash2,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { SUPPORTED_LANGUAGES } from "@/constants/languages";

export default function ProductDetailPage() {
  const params = useParams();
  const router = useRouter();
  const t = useTranslations("products");
  const locale = useLocale();
  const [selectedLanguage, setSelectedLanguage] = useState<string>(locale);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const productId = params.id as string;

  // Queries
  const product = useQuery(api.products.getProduct, {
    productId: productId as any,
    language: selectedLanguage,
  });

  const relatedProducts = useQuery(
    api.products.getSmartRelatedProducts,
    product ? {
      productId: product._id,
      language: selectedLanguage,
      limit: 4,
    } : "skip"
  );

  // Mutations
  const deleteProduct = useMutation(api.products.deleteProduct);
  const publishProduct = useMutation(api.products.publishProduct);

  // Cover Image Component
  const CoverImage = ({
    coverImageId,
    name,
    className = "",
  }: {
    coverImageId?: string;
    name: string;
    className?: string;
  }) => {
    const coverImageInfo = useQuery(
      api.media.getMediaFileByStorageId,
      coverImageId ? { storageId: coverImageId as any } : "skip",
    );

    if (!coverImageId || !coverImageInfo?.url) {
      return (
        <div className={`flex items-center justify-center bg-gray-100 ${className}`}>
          <Building2 className="h-12 w-12 text-gray-400" />
        </div>
      );
    }

    return (
      <div className={`relative overflow-hidden ${className}`}>
        <Image
          src={coverImageInfo.url}
          alt={name}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>
    );
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "published":
        return "bg-green-50 text-green-700 border-green-200";
      case "draft":
        return "bg-amber-50 text-amber-700 border-amber-200";
      case "discontinued":
        return "bg-red-50 text-red-700 border-red-200";
      default:
        return "bg-gray-50 text-gray-600 border-gray-200";
    }
  };

  const formatDate = (timestamp?: number) => {
    if (!timestamp) return t("detail.not_found");
    return new Date(timestamp).toLocaleDateString(
      selectedLanguage === "zh" ? "zh-CN" : "en-US",
      {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      },
    );
  };

  const handleDelete = async () => {
    if (!product) return;
    
    setIsDeleting(true);
    try {
      await deleteProduct({ productId: product._id });
      toast.success("产品删除成功");
      router.push("/dashboard/products");
    } catch (_error) {
      toast.error(t("detail.delete_failed"));
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  const handlePublish = async () => {
    if (!product) return;
    
    try {
      await publishProduct({ productId: product._id });
      toast.success(t("detail.publish_success"));
    } catch (_error) {
      toast.error(t("detail.publish_failed"));
    }
  };

  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50/50">
        <div className="mx-auto max-w-7xl p-6">
          <div className="mb-8 flex items-center gap-4">
            <Link href="/dashboard/products">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t("detail.actions.back")}
              </Button>
            </Link>
          </div>
          
          <Card className="p-8 text-center">
            <Building2 className="mx-auto mb-4 h-12 w-12 text-gray-400" />
            <h2 className="mb-2 font-semibold text-gray-900 text-xl">
              {t("detail.not_found")}
            </h2>
            <p className="mb-6 text-gray-600">{t("detail.not_found_description")}</p>
            <Link href="/dashboard/products">
              <Button>{t("shared.errors.back_to_products")}</Button>
            </Link>
          </Card>
        </div>
      </div>
    );
  }

  const productName = product.name[selectedLanguage] || Object.values(product.name)[0] || "";
  const productTitle = product.title?.[selectedLanguage] || Object.values(product.title || {})[0] || "";
  const productDescription = product.description[selectedLanguage] || Object.values(product.description)[0] || "";
  const productSpecifications = product.specifications?.[selectedLanguage] || Object.values(product.specifications || {})[0] || "";
  const productInstructions = product.instructions?.[selectedLanguage] || Object.values(product.instructions || {})[0] || "";

  return (
    <div className="min-h-screen bg-gray-50/50">
      <div className="mx-auto max-w-7xl space-y-8 p-6">
        {/* Header */}
        <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
            <Link href="/dashboard/products">
              <Button variant="ghost" size="sm" className="gap-2 self-start">
                <ArrowLeft className="h-4 w-4" />
                <span className="hidden sm:inline">
                  {t("detail.actions.back")}
                </span>
                <span className="sm:hidden">返回</span>
              </Button>
            </Link>

            <div className="hidden h-6 w-px bg-gray-300 sm:block" />

            <div>
              <h1 className="font-bold text-gray-900 text-xl sm:text-2xl">{productName}</h1>
              {productTitle && (
                <p className="text-gray-600 text-sm">{productTitle}</p>
              )}
            </div>
          </div>

          <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
            {/* Language Selector */}
            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger className="w-40 border-gray-200 focus:border-blue-400 focus:ring-blue-400">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {SUPPORTED_LANGUAGES
                  .filter(lang => product.availableLanguages.includes(lang.code))
                  .map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      {lang.nativeName}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>

            {/* Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="hidden sm:inline">操作</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>{t("detail.actions.title")}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href={`/dashboard/products/${product._id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    {t("detail.actions.edit_product")}
                  </Link>
                </DropdownMenuItem>
                {product.status === "draft" && (
                  <DropdownMenuItem onClick={handlePublish}>
                    <Eye className="mr-2 h-4 w-4" />
                    {t("detail.actions.publish")}
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setIsDeleteDialogOpen(true)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  {t("detail.actions.delete")}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Main Content */}
          <div className="space-y-6 lg:col-span-2">
            {/* Cover Image */}
            {product.coverImageId && (
              <Card className="overflow-hidden border-0 shadow-sm">
                <CoverImage
                  coverImageId={product.coverImageId}
                  name={productName}
                  className="h-64 w-full"
                />
              </Card>
            )}

            {/* Product Description */}
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>{t("detail.content.description")}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none">
                  <p className="whitespace-pre-wrap text-gray-700">{productDescription}</p>
                </div>
              </CardContent>
            </Card>

            {/* Product Specifications */}
            {productSpecifications && (
              <Card className="border-0 shadow-sm">
                <CardHeader>
                  <CardTitle>{t("detail.content.specifications")}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose max-w-none">
                    <p className="whitespace-pre-wrap text-gray-700">{productSpecifications}</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Usage Instructions */}
            {productInstructions && (
              <Card className="border-0 shadow-sm">
                <CardHeader>
                  <CardTitle>{t("detail.content.instructions")}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="prose max-w-none">
                    <p className="whitespace-pre-wrap text-gray-700">{productInstructions}</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Product Standards */}
            {product.standards && product.standards.length > 0 && (
              <Card className="border-0 shadow-sm">
                <CardHeader>
                  <CardTitle>{t("detail.content.standards")}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {product.standards.map((standard, index) => {
                    const standardType = standard.standardType[selectedLanguage] || 
                    Object.values(standard.standardType)[0] || "";
                    const standardValues = standard.standardValues[selectedLanguage] || 
                    Object.values(standard.standardValues)[0] || [];
                    
                    return (
                    <div key={`standard-${index}-${standardType}`} className="flex items-start gap-3 rounded-lg bg-gray-50 p-3">
                    <div className="flex-1">
                    <h4 className="font-medium text-gray-900 text-sm">{standardType}</h4>
                    <div className="mt-1 flex flex-wrap gap-1">
                    {standardValues.map((value, valueIndex) => (
                    <Badge key={`value-${index}-${valueIndex}-${value}`} variant="secondary" className="text-xs">
                    {value}
                    </Badge>
                    ))}
                    </div>
                    </div>
                    </div>
                    );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Related Products */}
            {relatedProducts && relatedProducts.length > 0 && (
              <Card className="border-0 shadow-sm">
                <CardHeader>
                  <CardTitle>{t("detail.content.smart_recommendations")}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    {relatedProducts.map((relatedProduct) => {
                      const relatedName = relatedProduct.name[selectedLanguage] || 
                                        Object.values(relatedProduct.name)[0] || "";
                      const relatedDescription = relatedProduct.description[selectedLanguage] || 
                                                Object.values(relatedProduct.description)[0] || "";
                      
                      return (
                        <Link
                          key={relatedProduct._id}
                          href={`/dashboard/products/${relatedProduct._id}`}
                          className="group"
                        >
                          <div className="rounded-lg border bg-white p-4 transition-all hover:shadow-md">
                            <div className="flex items-start gap-3">
                              <CoverImage
                                coverImageId={relatedProduct.coverImageId}
                                name={relatedName}
                                className="h-12 w-12 shrink-0 rounded-lg"
                              />
                              <div className="min-w-0 flex-1">
                                <h4 className="font-medium text-gray-900 text-sm transition-colors group-hover:text-blue-600">
                                  {relatedName}
                                </h4>
                                <p className="mt-1 line-clamp-2 text-gray-600 text-xs">
                                  {relatedDescription}
                                </p>
                                <div className="mt-2 flex items-center gap-2">
                                  <Badge variant="outline" className="text-xs">
                                    匹配度: {Math.round(relatedProduct.matchScore * 100)}%
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Product Info */}
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>{t("detail.sidebar.product_info")}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground text-sm">{t("detail.sidebar.status")}</span>
                  <Badge className={getStatusColor(product.status)}>
                    {t(`status.${product.status || "draft"}`)}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground text-sm">{t("detail.sidebar.category")}</span>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Tag className="h-3 w-3" />
                    {product.category.name[selectedLanguage] || Object.values(product.category.name)[0]}
                  </Badge>
                </div>

                {product.isFeatured && (
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground text-sm">推荐产品</span>
                    <Badge className="border-purple-200 bg-purple-50 text-purple-700">
                      <Star className="mr-1 h-3 w-3" />
                      {t("badges.featured")}
                    </Badge>
                  </div>
                )}

                <Separator />

                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">{t("detail.sidebar.created")}</span>
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(product._creationTime)}
                    </span>
                  </div>

                  {product.publishedAt && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">{t("detail.sidebar.published")}</span>
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {formatDate(product.publishedAt)}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Language Versions */}
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  {t("detail.sidebar.language_versions")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                {SUPPORTED_LANGUAGES
                .filter(lang => product.availableLanguages.includes(lang.code))
                .map((lang) => (
                <div key={lang.code} className="flex items-center justify-between">
                <span className="text-sm">{lang.nativeName}</span>
                <div className="flex items-center gap-1">
                {lang.code === product.originalLanguage && (
                <Badge variant="secondary" className="text-xs">
                {t("detail.original_language")}
                </Badge>
                )}
                <Button
                variant={selectedLanguage === lang.code ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedLanguage(lang.code)}
                className="h-6 px-2 text-xs"
                >
                {selectedLanguage === lang.code ? "当前" : "切换"}
                </Button>
                </div>
                </div>
                ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Delete Confirmation Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t("detail.confirm_delete")}</DialogTitle>
              <DialogDescription>{t("detail.delete_warning")}</DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                {t("detail.delete_cancel")}
              </Button>
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={isDeleting}
              >
                {isDeleting ? t("detail.deleting") : t("detail.delete_confirm")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
