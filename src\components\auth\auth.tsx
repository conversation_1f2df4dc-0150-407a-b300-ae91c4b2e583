"use client";

import { useState } from "react";
import type { AuthFlow } from "@/types/auth";
import { SignIn } from "./sign-in";
import { SignUp } from "./sign-up";

export function Auth() {
  const [state, setState] = useState<AuthFlow>("signIn");

  return (
    <div className="relative flex min-h-screen items-center justify-center overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 px-4 py-12 sm:px-6 lg:px-8">
      <div
        className="absolute inset-0 opacity-30"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e0e7ff' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='7'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}
      />
      <div className="relative z-10">
        {state === "signIn" ? (
          <SignIn setState={setState} />
        ) : (
          <SignUp setState={setState} />
        )}
      </div>
    </div>
  );
}
