import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// 定义多语言文本类型
const multiLanguageText = v.record(v.string(), v.string()); // Record<languageCode, text>

// 定义多语言数组类型
const multiLanguageArray = v.record(v.string(), v.array(v.string())); // Record<languageCode, string[]>

/**
 * 添加或更新产品标准
 */
export const setProductStandard = mutation({
  args: {
    productId: v.id("products"),
    standardType: multiLanguageText,
    standardValues: multiLanguageArray,
    sortOrder: v.optional(v.number()),
    originalLanguage: v.string(),
    availableLanguages: v.array(v.string()),
  },
  returns: v.id("product_standards"),
  handler: async (ctx, args) => {
    // 检查产品是否存在
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new Error("Product not found");
    }

    // 获取产品现有标准（简单检查基于产品ID）
    const existingStandards = await ctx.db
      .query("product_standards")
      .withIndex("by_product", (q) => q.eq("productId", args.productId))
      .collect();

    // 检查是否已存在相同原始语言的同类型标准
    const existingStandard = existingStandards.find(
      (s) =>
        s.originalLanguage === args.originalLanguage &&
        Object.keys(s.standardType)[0] === Object.keys(args.standardType)[0],
    );

    if (existingStandard) {
      // 更新现有标准
      await ctx.db.patch(existingStandard._id, {
        standardType: args.standardType,
        standardValues: args.standardValues,
        sortOrder: args.sortOrder,
        availableLanguages: args.availableLanguages,
      });
      return existingStandard._id;
    } else {
      // 创建新标准
      return await ctx.db.insert("product_standards", {
        productId: args.productId,
        standardType: args.standardType,
        standardValues: args.standardValues,
        sortOrder: args.sortOrder,
        originalLanguage: args.originalLanguage,
        availableLanguages: args.availableLanguages,
        isActive: true,
      });
    }
  },
});

/**
 * 批量设置产品标准
 */
export const batchSetProductStandards = mutation({
  args: {
    productId: v.id("products"),
    standards: v.array(
      v.object({
        standardType: multiLanguageText,
        standardValues: multiLanguageArray,
        sortOrder: v.optional(v.number()),
        originalLanguage: v.string(),
        availableLanguages: v.array(v.string()),
      }),
    ),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // 检查产品是否存在
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new Error("Product not found");
    }

    // 删除现有的所有标准
    const existingStandards = await ctx.db
      .query("product_standards")
      .withIndex("by_product", (q) => q.eq("productId", args.productId))
      .collect();

    for (const standard of existingStandards) {
      await ctx.db.delete(standard._id);
    }

    // 批量插入新标准
    for (const standard of args.standards) {
      await ctx.db.insert("product_standards", {
        productId: args.productId,
        standardType: standard.standardType,
        standardValues: standard.standardValues,
        sortOrder: standard.sortOrder,
        originalLanguage: standard.originalLanguage,
        availableLanguages: standard.availableLanguages,
        isActive: true,
      });
    }

    return null;
  },
});

/**
 * 获取产品的所有标准
 */
export const getProductStandards = query({
  args: {
    productId: v.id("products"),
  },
  returns: v.array(
    v.object({
      _id: v.id("product_standards"),
      _creationTime: v.number(),
      standardType: multiLanguageText,
      standardValues: multiLanguageArray,
      sortOrder: v.optional(v.number()),
      isActive: v.optional(v.boolean()),
      originalLanguage: v.string(),
      availableLanguages: v.array(v.string()),
    }),
  ),
  handler: async (ctx, args) => {
    const standards = await ctx.db
      .query("product_standards")
      .withIndex("by_product", (q) => q.eq("productId", args.productId))
      .filter((q) => q.neq(q.field("isActive"), false))
      .collect();

    // 按排序顺序排列，如果没有排序顺序则按创建时间排列
    return standards.sort((a, b) => {
      const orderA = a.sortOrder ?? a._creationTime;
      const orderB = b.sortOrder ?? b._creationTime;
      return orderA - orderB;
    });
  },
});

/**
 * 删除产品标准
 */
export const deleteProductStandard = mutation({
  args: {
    standardId: v.id("product_standards"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const standard = await ctx.db.get(args.standardId);
    if (!standard) {
      throw new Error("Standard not found");
    }

    await ctx.db.delete(args.standardId);
    return null;
  },
});

/**
 * 获取所有标准类型（用于前端选择器）
 */
export const getStandardTypes = query({
  args: {
    language: v.optional(v.string()), // 指定语言，如果不指定则返回所有语言的类型
  },
  returns: v.array(
    v.object({
      standardType: v.string(), // 标准类型名称（指定语言的）
      count: v.number(),
      availableLanguages: v.array(v.string()),
    }),
  ),
  handler: async (ctx, args) => {
    const allStandards = await ctx.db
      .query("product_standards")
      .filter((q) => q.neq(q.field("isActive"), false))
      .collect();

    const language = args.language || "en"; // 默认使用英文
    const typeCount: Record<string, { count: number; languages: Set<string> }> =
      {};

    for (const standard of allStandards) {
      // 获取指定语言的标准类型名称
      const typeName =
        standard.standardType[language] ||
        standard.standardType[standard.originalLanguage] ||
        Object.values(standard.standardType)[0];

      if (typeName) {
        if (!typeCount[typeName]) {
          typeCount[typeName] = { count: 0, languages: new Set() };
        }
        typeCount[typeName].count++;
        // 收集所有可用语言
        standard.availableLanguages.forEach((lang) =>
          typeCount[typeName].languages.add(lang),
        );
      }
    }

    return Object.entries(typeCount)
      .map(([standardType, { count, languages }]) => ({
        standardType,
        count,
        availableLanguages: Array.from(languages),
      }))
      .sort((a, b) => b.count - a.count); // 按使用频率排序
  },
});

/**
 * 根据标准类型搜索产品
 */
export const searchProductsByStandard = query({
  args: {
    standardType: v.string(),
    standardValue: v.optional(v.string()),
    language: v.optional(v.string()),
  },
  returns: v.array(
    v.object({
      _id: v.id("products"),
      name: v.string(),
      title: v.optional(v.string()),
      description: v.string(),
      coverImageId: v.optional(v.id("_storage")),
      matchedStandards: v.array(
        v.object({
          standardType: v.string(),
          standardValues: v.array(v.string()),
        }),
      ),
    }),
  ),
  handler: async (ctx, args) => {
    const language = args.language || "en";

    // 获取所有产品标准
    const allStandards = await ctx.db
      .query("product_standards")
      .filter((q) => q.neq(q.field("isActive"), false))
      .collect();

    // 筛选匹配的标准
    const matchedStandards = allStandards.filter((standard) => {
      // 检查标准类型是否匹配
      const typeName =
        standard.standardType[language] ||
        standard.standardType[standard.originalLanguage] ||
        Object.values(standard.standardType)[0];

      if (typeName !== args.standardType) {
        return false;
      }

      // 如果指定了标准值，进一步筛选
      if (args.standardValue) {
        const targetValue = args.standardValue.toLowerCase();
        const standardValues =
          standard.standardValues[language] ||
          standard.standardValues[standard.originalLanguage] ||
          Object.values(standard.standardValues)[0] ||
          [];

        return standardValues.some((value) =>
          value.toLowerCase().includes(targetValue),
        );
      }

      return true;
    });

    // 获取匹配的产品
    const productIds = [...new Set(matchedStandards.map((s) => s.productId))];
    const products = await Promise.all(
      productIds.map(async (productId) => {
        const product = await ctx.db.get(productId);
        if (!product) return null;

        // 获取该产品的匹配标准
        const productMatchedStandards = matchedStandards
          .filter((s) => s.productId === productId)
          .map((s) => {
            const typeName =
              s.standardType[language] ||
              s.standardType[s.originalLanguage] ||
              Object.values(s.standardType)[0];
            const values =
              s.standardValues[language] ||
              s.standardValues[s.originalLanguage] ||
              Object.values(s.standardValues)[0] ||
              [];

            return {
              standardType: typeName,
              standardValues: values,
            };
          });

        // 获取产品信息（指定语言）
        const productName =
          product.name[language] ||
          product.name[product.originalLanguage] ||
          Object.values(product.name)[0];
        const productTitle = product.title
          ? product.title[language] ||
            product.title[product.originalLanguage] ||
            Object.values(product.title)[0]
          : undefined;
        const productDescription =
          product.description[language] ||
          product.description[product.originalLanguage] ||
          Object.values(product.description)[0];

        return {
          _id: product._id,
          name: productName,
          title: productTitle,
          description: productDescription,
          coverImageId: product.coverImageId,
          matchedStandards: productMatchedStandards,
        };
      }),
    );

    return products.filter((p) => p !== null);
  },
});
