import { authTables } from "@convex-dev/auth/server";
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

// 定义多语言文本类型
const multiLanguageText = v.record(v.string(), v.string()); // Record<languageCode, text>

// 定义多语言数组类型
const multiLanguageArray = v.record(v.string(), v.array(v.string())); // Record<languageCode, string[]>

export default defineSchema({
  ...authTables,

  // 语言配置表
  languages: defineTable({
    code: v.string(), // 语言代码，如 'en', 'zh', 'ja'
    name: v.string(), // 语言名称，如 'English', '中文', '日本語'
    nativeName: v.string(), // 本地语言名称
    isActive: v.boolean(), // 是否启用该语言
    isDefault: v.boolean(), // 是否为默认语言
    sortOrder: v.optional(v.number()), // 排序
  })
    .index("by_code", ["code"])
    .index("by_active", ["isActive"])
    .index("by_default", ["isDefault"])
    .index("by_sort_order", ["sortOrder"]),

  tasks: defineTable({
    text: v.string(),
    isCompleted: v.boolean(),
    userId: v.optional(v.string()),
  }).index("by_user_id", ["userId"]),

  news_categories: defineTable({
    // 多语言名称和描述
    name: multiLanguageText,
    description: multiLanguageText,
    // 分类排序
    sortOrder: v.optional(v.number()),
    // 是否启用
    isActive: v.optional(v.boolean()),
    // SEO相关 - 按语言分别设置
    seoKeywords: v.optional(multiLanguageArray),
    seoDescription: v.optional(multiLanguageText),
    // URL slug - 按语言分别设置
    slug: v.optional(multiLanguageText),
  })
    .index("by_sort_order", ["sortOrder"])
    .index("by_active", ["isActive"]),

  news_articles: defineTable({
    // 多语言标题和内容
    title: multiLanguageText,
    content: multiLanguageText,
    // 文章摘要
    summary: v.optional(multiLanguageText),
    // 封面图片
    coverImageId: v.optional(v.id("_storage")),
    // 文章图片集合
    imageIds: v.optional(v.array(v.id("_storage"))),
    // 分类ID
    categoryId: v.id("news_categories"),
    // 文章状态
    status: v.optional(
      v.union(
        v.literal("draft"), // 草稿
        v.literal("published"), // 已发布
        v.literal("archived"), // 已归档
      ),
    ),
    // 发布时间
    publishedAt: v.optional(v.number()),
    // 排序权重
    sortOrder: v.optional(v.number()),
    // 浏览次数
    viewCount: v.optional(v.number()),
    // SEO相关 - 按语言分别设置
    seoKeywords: v.optional(multiLanguageArray),
    seoDescription: v.optional(multiLanguageText),
    // URL slug - 按语言分别设置
    slug: v.optional(multiLanguageText),
    // 原始创建语言
    originalLanguage: v.string(),
    // 可用语言列表
    availableLanguages: v.array(v.string()),
  })
    .index("by_category", ["categoryId"])
    .index("by_status", ["status"])
    .index("by_published_at", ["publishedAt"])
    .index("by_sort_order", ["sortOrder"])
    .index("by_original_language", ["originalLanguage"]),

  products_categories: defineTable({
    // 多语言名称和描述
    name: multiLanguageText,
    description: multiLanguageText,
    sortOrder: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
    // SEO相关 - 按语言分别设置
    seoKeywords: v.optional(multiLanguageArray),
    seoDescription: v.optional(multiLanguageText),
    // URL slug - 按语言分别设置
    slug: v.optional(multiLanguageText),
  })
    .index("by_sort_order", ["sortOrder"])
    .index("by_active", ["isActive"]),

  products: defineTable({
    // 多语言名称、标题和描述
    name: multiLanguageText,
    title: v.optional(multiLanguageText),
    description: multiLanguageText,
    // 产品规格等详细信息
    specifications: v.optional(multiLanguageText),
    // 使用说明
    instructions: v.optional(multiLanguageText),
    // 封面图片
    coverImageId: v.optional(v.id("_storage")),
    categoryId: v.id("products_categories"),
    imageIds: v.optional(v.array(v.id("_storage"))),
    status: v.optional(
      v.union(
        v.literal("draft"),
        v.literal("published"),
        v.literal("discontinued"),
      ),
    ),
    // SEO相关 - 按语言分别设置
    seoKeywords: v.optional(multiLanguageArray),
    seoDescription: v.optional(multiLanguageText),
    // URL slug - 按语言分别设置
    slug: v.optional(multiLanguageText),
    sortOrder: v.optional(v.number()),
    isFeatured: v.optional(v.boolean()),
    publishedAt: v.optional(v.number()),
    // 原始创建语言
    originalLanguage: v.string(),
    // 可用语言列表
    availableLanguages: v.array(v.string()),
  })
    .index("by_category", ["categoryId"])
    .index("by_status", ["status"])
    .index("by_featured", ["isFeatured"])
    .index("by_published_at", ["publishedAt"])
    .index("by_sort_order", ["sortOrder"])
    .index("by_original_language", ["originalLanguage"]),

  product_standards: defineTable({
    productId: v.id("products"),
    // 标准类型 - 多语言
    standardType: multiLanguageText,
    // 标准值 - 多语言
    standardValues: multiLanguageArray,
    sortOrder: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
    // 原始创建语言
    originalLanguage: v.string(),
    // 可用语言列表
    availableLanguages: v.array(v.string()),
  })
    .index("by_product", ["productId"])
    .index("by_original_language", ["originalLanguage"]),

  // 媒体文件管理
  media_files: defineTable({
    // 原始文件名
    originalName: v.string(),
    // 文件大小（字节）
    size: v.number(),
    // 文件类型
    contentType: v.string(),
    // 文件存储ID（Convex storage ID）
    storageId: v.id("_storage"),
    // 文件分类
    category: v.union(
      v.literal("image"), // 图片
      v.literal("video"), // 视频
      v.literal("document"), // 文档
      v.literal("audio"), // 音频
      v.literal("other"), // 其他
    ),
    // 文件标签（用于分类和搜索）
    tags: v.optional(v.array(v.string())),
    // 文件描述
    description: v.optional(v.string()),
    // Alt文本（用于图片）
    altText: v.optional(v.string()),
    // 是否公开
    isPublic: v.optional(v.boolean()),
    // 上传者用户ID
    uploadedBy: v.optional(v.string()),
    // 文件访问次数
    accessCount: v.optional(v.number()),
    // 最后访问时间
    lastAccessedAt: v.optional(v.number()),
  })
    .index("by_category", ["category"])
    .index("by_content_type", ["contentType"])
    .index("by_uploaded_by", ["uploadedBy"])
    .index("by_tags", ["tags"])
    .index("by_public", ["isPublic"])
    .index("by_storage_id", ["storageId"]),

  // 媒体文件夹/分组
  media_folders: defineTable({
    // 文件夹名称
    name: v.string(),
    // 文件夹描述
    description: v.optional(v.string()),
    // 父文件夹ID（支持嵌套）
    parentFolderId: v.optional(v.id("media_folders")),
    // 文件夹颜色标识
    color: v.optional(v.string()),
    // 排序权重
    sortOrder: v.optional(v.number()),
    // 创建者用户ID
    createdBy: v.optional(v.string()),
  })
    .index("by_parent", ["parentFolderId"])
    .index("by_created_by", ["createdBy"])
    .index("by_sort_order", ["sortOrder"]),

  // 媒体文件与文件夹的关联
  media_file_folders: defineTable({
    fileId: v.id("media_files"),
    folderId: v.id("media_folders"),
  })
    .index("by_file", ["fileId"])
    .index("by_folder", ["folderId"]),
});
